# Malombo Admin Panel - Database Setup

This folder contains SQL scripts to set up the authentication subsystem for the Malombo Selous Forest Camp admin panel.

## 📁 File Structure

```
database/
├── README.md                    # This file
├── 01_create_profiles_table.sql # Create profiles table with user roles
├── 02_setup_rls_policies.sql    # Set up Row Level Security policies
├── 03_create_triggers.sql       # Create database triggers
├── 04_seed_admin_users.sql      # Insert initial admin user data
├── 05_create_accommodations_table.sql # Create accommodations table and storage
├── 06_accommodations_rls_functions.sql # Accommodations RLS policies
├── 07_create_activities_table.sql # Create activities table and storage
├── 08_activities_rls_functions.sql # Activities RLS policies
├── 09_create_bookings_table.sql # Create bookings table with full schema
├── 10_bookings_rls_functions.sql # Bookings RLS policies and functions
├── 11_create_settings_table.sql # Create settings table for configuration
└── 12_settings_rls_functions.sql # Settings RLS policies and functions
```

## 🚀 Quick Setup

### Step 1: Run SQL Scripts in Order

Execute these scripts in your Supabase SQL Editor in the following order:

1. **Create Profiles Table**

   ```sql
   -- Copy and paste content from 01_create_profiles_table.sql
   ```

2. **Setup RLS Policies**

   ```sql
   -- Copy and paste content from 02_setup_rls_policies.sql
   ```

3. **Create Triggers**

   ```sql
   -- Copy and paste content from 03_create_triggers.sql
   ```

4. **Seed Admin Users** (after creating auth users)
   ```sql
   -- Copy and paste content from 04_seed_admin_users.sql
   ```

### Step 2: Create Test Users in Supabase Auth

1. Go to your Supabase Dashboard → Authentication → Users
2. Click "Add User" and create these test accounts:
   - **Email**: `<EMAIL>`, **Password**: `Admin123!`
   - **Email**: `<EMAIL>`, **Password**: `Staff123!`
   - **Email**: `<EMAIL>`, **Password**: `Manager123!`

### Step 3: Update Seed Script with Real UUIDs

1. Run this query to get the user UUIDs:

   ```sql
   SELECT id, email FROM auth.users
   WHERE email IN ('<EMAIL>', '<EMAIL>', '<EMAIL>');
   ```

2. Update `04_seed_admin_users.sql` with the actual UUIDs
3. Re-run the seed script

## 📋 What Each Script Does

### 01_create_profiles_table.sql

- Creates the `profiles` table that extends Supabase Auth
- Adds user roles (`admin`, `staff`)
- Includes metadata fields (name, phone, etc.)
- Sets up indexes for performance
- Grants necessary permissions

### 02_setup_rls_policies.sql

- Enables Row Level Security on profiles table
- Creates **PERMISSIVE** policies for development
- Includes commented production-ready policies
- ⚠️ **WARNING**: Development policies are NOT secure for production!

### 03_create_triggers.sql

- Creates `update_updated_at_column()` function for automatic timestamps
- Creates `handle_new_user()` function for automatic profile creation
- Sets up triggers for automatic operations
- Handles errors gracefully

### 04_seed_admin_users.sql

- Inserts test user profiles
- Provides example data structure
- Includes helper queries for verification
- Safe to run multiple times (idempotent)

## 🔒 Security Notes

### Development vs Production

**Current Setup (Development)**:

- ✅ Permissive RLS policies for easy testing
- ✅ All authenticated users can read/write profiles
- ✅ Simple role-based access control

**Production Requirements**:

- 🔐 Restrict profile access to own profile + admin override
- 🔐 Only admins can create/delete users
- 🔐 Implement proper audit logging
- 🔐 Add rate limiting and input validation

### Switching to Production

1. Replace development policies with production ones in `02_setup_rls_policies.sql`
2. Remove or secure test accounts
3. Implement additional security measures
4. Add proper error handling and logging

## 🧪 Testing the Setup

### Verify Database Structure

```sql
-- Check if profiles table exists
SELECT table_name, column_name, data_type
FROM information_schema.columns
WHERE table_name = 'profiles';

-- Check RLS policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual
FROM pg_policies
WHERE tablename = 'profiles';

-- Check triggers
SELECT trigger_name, event_manipulation, event_object_table
FROM information_schema.triggers
WHERE event_object_table = 'profiles';
```

### Test User Creation

```sql
-- View all profiles
SELECT id, email, role, first_name, last_name, is_active, created_at
FROM public.profiles
ORDER BY role DESC, email;

-- Test role-based queries
SELECT COUNT(*) as admin_count FROM public.profiles WHERE role = 'admin';
SELECT COUNT(*) as staff_count FROM public.profiles WHERE role = 'staff';
```

## 🛠 Troubleshooting

### Common Issues

1. **"relation does not exist" error**

   - Make sure you're running scripts in the correct order
   - Check that you're in the `public` schema

2. **"permission denied" error**

   - Verify RLS policies are set up correctly
   - Check user authentication status

3. **Trigger not firing**

   - Verify triggers are created: `\dS` in psql
   - Check function permissions

4. **UUID mismatch in seed data**
   - Get real UUIDs from `auth.users` table
   - Update seed script with actual values

### Getting Help

- Check Supabase logs in Dashboard → Logs
- Use `RAISE NOTICE` statements for debugging
- Verify permissions with `\dp profiles` in psql

## 📚 Additional Resources

- [Supabase Auth Documentation](https://supabase.com/docs/guides/auth)
- [Row Level Security Guide](https://supabase.com/docs/guides/auth/row-level-security)
- [PostgreSQL Triggers](https://www.postgresql.org/docs/current/triggers.html)

---

**Ready to test?** Start with `01_create_profiles_table.sql` and work your way through! 🚀
