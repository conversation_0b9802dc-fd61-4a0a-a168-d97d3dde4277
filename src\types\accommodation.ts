// =====================================================
// Accommodation Types and Interfaces
// =====================================================
// Comprehensive TypeScript definitions for accommodation management

// Accommodation types enum
export type AccommodationType = 
  | 'banda' 
  | 'room' 
  | 'tent' 
  | 'tree_house' 
  | 'campsite' 
  | 'suite' 
  | 'lodge' 
  | 'cabin';

// Accommodation status enum
export type AccommodationStatus = 'draft' | 'published' | 'unpublished';

// Main accommodation interface (matches database schema)
export interface Accommodation {
  id: string;
  name: string;
  type: AccommodationType;
  description: string;
  special_features?: string;
  amenities: string[];
  price_range: string;
  capacity: number;
  images: string[];
  status: AccommodationStatus;
  featured: boolean;
  created_at: string;
  updated_at: string;
  created_by?: string;
  updated_by?: string;
}

// Form data interface for creating/editing accommodations
export interface AccommodationFormData {
  name: string;
  type: AccommodationType;
  description: string;
  special_features: string;
  amenities: string[];
  price_range: string;
  capacity: number;
  images: File[];
  existing_images: string[];
  status: AccommodationStatus;
  featured: boolean;
}

// Interface for accommodation list filters
export interface AccommodationFilters {
  search: string;
  type: AccommodationType | 'all';
  status: AccommodationStatus | 'all';
  featured: boolean | 'all';
}

// Interface for accommodation list sorting
export interface AccommodationSort {
  field: 'name' | 'type' | 'price_range' | 'capacity' | 'created_at' | 'updated_at';
  direction: 'asc' | 'desc';
}

// Interface for pagination
export interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

// API response interfaces
export interface AccommodationListResponse {
  data: Accommodation[];
  pagination: Pagination;
  filters: AccommodationFilters;
  sort: AccommodationSort;
}

export interface AccommodationResponse {
  data: Accommodation;
  success: boolean;
  message?: string;
}

export interface AccommodationCreateRequest {
  name: string;
  type: AccommodationType;
  description: string;
  special_features?: string;
  amenities: string[];
  price_range: string;
  capacity: number;
  status: AccommodationStatus;
  featured: boolean;
}

export interface AccommodationUpdateRequest extends Partial<AccommodationCreateRequest> {
  id: string;
}

// Image upload interfaces
export interface ImageUploadResponse {
  url: string;
  path: string;
  success: boolean;
  message?: string;
}

export interface ImageDeleteRequest {
  path: string;
}

// Predefined amenities list for form dropdowns
export const ACCOMMODATION_AMENITIES = [
  'Private bathroom',
  'Shared bathroom',
  'Hot water shower',
  'Heated shower',
  'Air conditioning',
  'Fan',
  'Minibar',
  'Private balcony',
  'Shared balcony',
  'Mosquito netting',
  'King-size bed',
  'Twin beds',
  'Sofa bed',
  'Wheelchair accessible',
  'Inter-leading rooms',
  'Private deck',
  'River view',
  'Forest view',
  'Wildlife viewing area',
  'Campfire area',
  'Solar power',
  'Generator backup',
  'WiFi',
  'Safe',
  'Tea/coffee facilities',
  'Meals included',
  'Butler service',
  'Laundry service',
  'Game drives included',
  'Guided walks included'
] as const;

// Accommodation type display names
export const ACCOMMODATION_TYPE_LABELS: Record<AccommodationType, string> = {
  banda: 'Banda',
  room: 'Room',
  tent: 'Tent',
  tree_house: 'Tree House',
  campsite: 'Campsite',
  suite: 'Suite',
  lodge: 'Lodge',
  cabin: 'Cabin'
};

// Status display configuration
export const ACCOMMODATION_STATUS_CONFIG: Record<AccommodationStatus, {
  label: string;
  color: string;
  bgColor: string;
}> = {
  draft: {
    label: 'Draft',
    color: 'text-gray-600',
    bgColor: 'bg-gray-100'
  },
  published: {
    label: 'Published',
    color: 'text-green-700',
    bgColor: 'bg-green-100'
  },
  unpublished: {
    label: 'Unpublished',
    color: 'text-red-700',
    bgColor: 'bg-red-100'
  }
};

// Form validation schemas (for use with react-hook-form)
export interface AccommodationFormErrors {
  name?: string;
  type?: string;
  description?: string;
  price_range?: string;
  capacity?: string;
  images?: string;
  amenities?: string;
  general?: string;
}

// Default form values
export const DEFAULT_ACCOMMODATION_FORM: AccommodationFormData = {
  name: '',
  type: 'room',
  description: '',
  special_features: '',
  amenities: [],
  price_range: '',
  capacity: 1,
  images: [],
  existing_images: [],
  status: 'draft',
  featured: false
};

// Default filters
export const DEFAULT_ACCOMMODATION_FILTERS: AccommodationFilters = {
  search: '',
  type: 'all',
  status: 'all',
  featured: 'all'
};

// Default sort
export const DEFAULT_ACCOMMODATION_SORT: AccommodationSort = {
  field: 'created_at',
  direction: 'desc'
};

// Default pagination
export const DEFAULT_PAGINATION: Pagination = {
  page: 1,
  limit: 10,
  total: 0,
  totalPages: 0
};

// Utility type for accommodation table row display
export interface AccommodationTableRow {
  id: string;
  name: string;
  type: string;
  price_range: string;
  capacity: number;
  status: AccommodationStatus;
  featured: boolean;
  created_at: string;
  image_count: number;
}

// Bulk operations interface
export interface BulkAccommodationOperation {
  action: 'publish' | 'unpublish' | 'delete' | 'feature' | 'unfeature';
  accommodation_ids: string[];
}

export interface BulkOperationResponse {
  success: boolean;
  updated_count: number;
  failed_count: number;
  message: string;
  errors?: string[];
}
