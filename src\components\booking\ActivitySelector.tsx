import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import {
  Clock,
  Users,
  AlertCircle,
  DollarSign,
  Star,
  Camera,
  Compass,
  TreePine,
  Mountain,
  Waves,
} from "lucide-react";
import type { Activity } from "@/types/booking";
import { formatPrice } from "@/lib/currency";

interface ActivitySelectorProps {
  activities: Activity[];
  selectedActivities: Activity[];
  onActivityToggle: (activity: Activity) => void;
  guestCount: number;
  loading?: boolean;
}

export function ActivitySelector({
  activities,
  selectedActivities,
  onActivityToggle,
  guestCount,
  loading = false,
}: ActivitySelectorProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>("all");

  const categories = [
    { id: "all", name: "All Activities", icon: <Star className="h-4 w-4" /> },
    {
      id: "safari",
      name: "Safari Adventures",
      icon: <Camera className="h-4 w-4" />,
    },
    {
      id: "cultural",
      name: "Cultural Experiences",
      icon: <Compass className="h-4 w-4" />,
    },
    {
      id: "adventure",
      name: "Adventure Activities",
      icon: <Mountain className="h-4 w-4" />,
    },
    {
      id: "wildlife",
      name: "Wildlife Encounters",
      icon: <TreePine className="h-4 w-4" />,
    },
    {
      id: "relaxation",
      name: "Relaxation & Wellness",
      icon: <Waves className="h-4 w-4" />,
    },
  ];

  const filteredActivities = activities.filter(
    (activity) =>
      selectedCategory === "all" || activity.category === selectedCategory
  );

  const isActivitySelected = (activityId: string) =>
    selectedActivities.some((activity) => activity.id === activityId);

  const getTotalActivitiesPrice = () =>
    selectedActivities.reduce(
      (total, activity) => total + activity.price * guestCount,
      0
    );

  const getCategoryIcon = (category: string) => {
    switch (category.toLowerCase()) {
      case "safari":
        return <Camera className="h-5 w-5 text-amber-600" />;
      case "cultural":
        return <Compass className="h-5 w-5 text-purple-600" />;
      case "adventure":
        return <Mountain className="h-5 w-5 text-green-600" />;
      case "relaxation":
        return <Waves className="h-5 w-5 text-blue-600" />;
      case "wildlife":
        return <TreePine className="h-5 w-5 text-emerald-600" />;
      default:
        return <Star className="h-5 w-5 text-gray-600" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category.toLowerCase()) {
      case "safari":
        return "bg-amber-100 text-amber-800 border-amber-200";
      case "cultural":
        return "bg-purple-100 text-purple-800 border-purple-200";
      case "adventure":
        return "bg-green-100 text-green-800 border-green-200";
      case "relaxation":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "wildlife":
        return "bg-emerald-100 text-emerald-800 border-emerald-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#2C5530]"></div>
        <span className="ml-2 text-gray-600">Loading activities...</span>
      </div>
    );
  }

  if (activities.length === 0) {
    return (
      <div className="text-center py-12">
        <TreePine className="h-16 w-16 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-600 mb-2">
          No Activities Available
        </h3>
        <p className="text-gray-500">
          Activities will be displayed here once they are added by
          administrators.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Category Filter */}
      <div className="flex flex-wrap gap-2 mb-6">
        {categories.map((category) => (
          <Button
            key={category.id}
            variant={selectedCategory === category.id ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedCategory(category.id)}
            className={
              selectedCategory === category.id
                ? "bg-[#2C5530] hover:bg-[#8B4513] text-white"
                : "border-[#2C5530] text-[#2C5530] hover:bg-[#2C5530] hover:text-white"
            }
          >
            {category.icon}
            <span className="ml-2">{category.name}</span>
          </Button>
        ))}
      </div>

      {/* Selected Activities Summary */}
      {selectedActivities.length > 0 && (
        <Card className="border-[#DAA520] bg-[#DAA520]/5">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg text-[#2C5530]">
              Selected Activities ({selectedActivities.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {selectedActivities.map((activity) => (
                <div
                  key={activity.id}
                  className="flex justify-between items-center text-sm"
                >
                  <span>{activity.name}</span>
                  <span className="font-semibold text-[#8B4513]">
                    {formatPrice(activity.price)} × {guestCount} ={" "}
                    {formatPrice(activity.price * guestCount)}
                  </span>
                </div>
              ))}
              <div className="border-t pt-2 flex justify-between items-center font-bold text-[#2C5530]">
                <span>Total Activities Cost:</span>
                <span>{formatPrice(getTotalActivitiesPrice())}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Activities List */}
      <div className="space-y-3">
        {filteredActivities.map((activity) => {
          const isSelected = isActivitySelected(activity.id);
          const isOverCapacity =
            activity.maxParticipants && guestCount > activity.maxParticipants;

          return (
            <Card
              key={activity.id}
              className={`group transition-all duration-300 cursor-pointer hover:shadow-md ${
                isSelected
                  ? "border-[#2C5530] bg-[#2C5530]/5 shadow-md"
                  : "border-neutral-200 hover:border-[#DAA520] hover:shadow-md"
              } ${isOverCapacity ? "opacity-60" : ""}`}
              onClick={() => !isOverCapacity && onActivityToggle(activity)}
            >
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4 flex-1">
                    <Checkbox
                      checked={isSelected}
                      disabled={isOverCapacity !== 0 && isOverCapacity}
                      className="data-[state=checked]:bg-[#2C5530] data-[state=checked]:border-[#2C5530]"
                    />
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        {getCategoryIcon(activity.category)}
                        <Badge
                          className={`text-xs ${getCategoryColor(
                            activity.category
                          )}`}
                        >
                          {activity.category.charAt(0).toUpperCase() +
                            activity.category.slice(1)}
                        </Badge>
                        {isSelected && (
                          <Badge className="bg-[#2C5530] text-white text-xs">
                            Selected
                          </Badge>
                        )}
                        {isOverCapacity && (
                          <Badge variant="destructive" className="text-xs">
                            Exceeds capacity
                          </Badge>
                        )}
                      </div>
                      <h3 className="font-semibold text-[#2C5530] group-hover:text-[#8B4513] transition-colors mb-1">
                        {activity.name}
                      </h3>
                      <p className="text-sm text-neutral-600 mb-2 line-clamp-2">
                        {activity.description}
                      </p>
                      <div className="flex items-center gap-4 text-xs text-neutral-500">
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          {activity.duration}
                        </div>
                        <div className="flex items-center gap-1">
                          <Users className="h-3 w-3" />
                          Up to {activity.maxParticipants || 8}
                        </div>
                        {activity.ageRestriction && (
                          <div className="flex items-center gap-1">
                            <AlertCircle className="h-3 w-3" />
                            <span>{activity.ageRestriction}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="text-right flex-shrink-0 ml-4">
                    <div className="text-lg font-bold text-[#8B4513]">
                      {formatPrice(activity.price)}
                    </div>
                    <div className="text-xs text-neutral-500">per person</div>
                    {activity.included && (
                      <Badge className="bg-green-100 text-green-800 text-xs mt-1">
                        Included
                      </Badge>
                    )}
                    <div className="text-sm font-semibold text-[#2C5530] mt-1">
                      Total: {formatPrice(activity.price * guestCount)}
                    </div>
                  </div>
                </div>

                {isOverCapacity && (
                  <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                    This activity has a maximum of {activity.maxParticipants}{" "}
                    participants. Your group has {guestCount} people.
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {filteredActivities.length === 0 && (
        <div className="text-center py-8 text-neutral-500">
          No activities found in this category.
        </div>
      )}
    </div>
  );
}
