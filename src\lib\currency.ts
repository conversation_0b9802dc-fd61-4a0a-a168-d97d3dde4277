// =====================================================
// Currency Formatting Utilities
// =====================================================
// Utilities for formatting currency values, especially TZS

import { CURRENCY_CONFIG } from "@/types/booking";
import type { Currency } from "@/types/booking";

/**
 * Format currency amount with proper symbol and formatting
 */
export function formatCurrency(
  amount: number,
  currency: Currency = "TZS",
  options: {
    showSymbol?: boolean;
    symbolPosition?: "before" | "after";
    thousandsSeparator?: string;
    decimalPlaces?: number;
  } = {}
): string {
  const config = CURRENCY_CONFIG[currency];
  const {
    showSymbol = true,
    symbolPosition = "before",
    thousandsSeparator = ",",
    decimalPlaces = config.decimals,
  } = options;

  // Format the number with proper decimal places
  const formattedAmount = amount.toLocaleString("en-US", {
    minimumFractionDigits: decimalPlaces,
    maximumFractionDigits: decimalPlaces,
    useGrouping: true,
  }).replace(/,/g, thousandsSeparator);

  if (!showSymbol) {
    return formattedAmount;
  }

  // For TZS, we prefer "TZS 50,000" format
  if (currency === "TZS") {
    return `TZS ${formattedAmount}`;
  }

  // For other currencies, use symbol position preference
  if (symbolPosition === "after") {
    return `${formattedAmount} ${config.symbol}`;
  } else {
    return `${config.symbol}${formattedAmount}`;
  }
}

/**
 * Format currency for display in components
 */
export function formatPrice(
  amount: number,
  currency: Currency = "TZS"
): string {
  return formatCurrency(amount, currency, {
    showSymbol: true,
    symbolPosition: currency === "TZS" ? "before" : "before",
  });
}

/**
 * Format currency for compact display (e.g., in cards)
 */
export function formatPriceCompact(
  amount: number,
  currency: Currency = "TZS"
): string {
  if (currency === "TZS") {
    // For large TZS amounts, show in thousands
    if (amount >= 1000000) {
      return `TZS ${(amount / 1000000).toFixed(1)}M`;
    } else if (amount >= 1000) {
      return `TZS ${(amount / 1000).toFixed(0)}K`;
    }
  }
  
  return formatCurrency(amount, currency);
}

/**
 * Parse currency string back to number
 */
export function parseCurrency(
  currencyString: string,
  currency: Currency = "TZS"
): number {
  const config = CURRENCY_CONFIG[currency];
  
  // Remove currency symbols and separators
  let cleanString = currencyString
    .replace(new RegExp(`${config.symbol}|TZS|USD|EUR|GBP`, "gi"), "")
    .replace(/[,\s]/g, "")
    .trim();

  const parsed = parseFloat(cleanString);
  return isNaN(parsed) ? 0 : parsed;
}

/**
 * Convert between currencies (placeholder - would integrate with exchange rate API)
 */
export function convertCurrency(
  amount: number,
  fromCurrency: Currency,
  toCurrency: Currency
): number {
  // This is a placeholder implementation
  // In a real application, you would integrate with an exchange rate API
  
  if (fromCurrency === toCurrency) {
    return amount;
  }

  // Placeholder exchange rates (these should come from a real API)
  const exchangeRates: Record<string, number> = {
    "USD_TZS": 2300,
    "EUR_TZS": 2500,
    "GBP_TZS": 2900,
    "TZS_USD": 1 / 2300,
    "TZS_EUR": 1 / 2500,
    "TZS_GBP": 1 / 2900,
  };

  const rateKey = `${fromCurrency}_${toCurrency}`;
  const rate = exchangeRates[rateKey];

  if (rate) {
    return amount * rate;
  }

  // If direct conversion not available, convert through USD
  if (fromCurrency !== "USD" && toCurrency !== "USD") {
    const toUSD = convertCurrency(amount, fromCurrency, "USD");
    return convertCurrency(toUSD, "USD", toCurrency);
  }

  return amount; // Fallback
}

/**
 * Get currency symbol for a given currency
 */
export function getCurrencySymbol(currency: Currency): string {
  return CURRENCY_CONFIG[currency]?.symbol || currency;
}

/**
 * Get currency name for a given currency
 */
export function getCurrencyName(currency: Currency): string {
  return CURRENCY_CONFIG[currency]?.name || currency;
}

/**
 * Check if currency uses decimals
 */
export function currencyUsesDecimals(currency: Currency): boolean {
  return CURRENCY_CONFIG[currency]?.decimals > 0;
}
