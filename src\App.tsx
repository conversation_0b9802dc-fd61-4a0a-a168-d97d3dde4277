import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import { Toaster } from "sonner";
import Home from "./pages/Home";
import Accommodations from "./pages/Accommodations";
import Booking from "./pages/Booking";
import NotFound from "./pages/NotFound";
import ErrorBoundary from "./components/ErrorBoundary";
import { AuthProvider } from "./components/auth/AuthProvider";
import { ProtectedRoute } from "./components/auth/ProtectedRoute";
import { RootLayout } from "./layouts/RootLayout";
import { AdminLayout } from "./layouts/AdminLayout";

// Admin pages
import Login from "./pages/admin/Login";
import Dashboard from "./pages/admin/Dashboard";
import AccommodationsAdmin from "./pages/admin/AccommodationsAdmin";
import AccommodationForm from "./pages/admin/AccommodationForm";
import ActivitiesAdmin from "./pages/admin/ActivitiesAdmin";
import ActivityForm from "./pages/admin/ActivityForm";
import BookingsAdmin from "./pages/admin/BookingsAdmin";
import SettingsAdmin from "./pages/admin/SettingsAdmin";

import "./App.css";

function App() {
  return (
    <ErrorBoundary>
      <AuthProvider>
        <Router>
          <Routes>
            {/* Public routes */}
            <Route
              path="/"
              element={
                <RootLayout>
                  <Home />
                </RootLayout>
              }
            />
            <Route
              path="/accommodations"
              element={
                <RootLayout>
                  <Accommodations />
                </RootLayout>
              }
            />
            <Route
              path="/booking"
              element={
                <RootLayout>
                  <Booking />
                </RootLayout>
              }
            />

            {/* Admin routes */}
            <Route path="/admin/login" element={<Login />} />
            <Route
              path="/admin/dashboard"
              element={
                <ProtectedRoute>
                  <AdminLayout>
                    <Dashboard />
                  </AdminLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/accommodations"
              element={
                <ProtectedRoute>
                  <AdminLayout>
                    <AccommodationsAdmin />
                  </AdminLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/accommodations/new"
              element={
                <ProtectedRoute>
                  <AdminLayout>
                    <AccommodationForm mode="create" />
                  </AdminLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/accommodations/edit/:id"
              element={
                <ProtectedRoute>
                  <AdminLayout>
                    <AccommodationForm mode="edit" />
                  </AdminLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/activities"
              element={
                <ProtectedRoute>
                  <AdminLayout>
                    <ActivitiesAdmin />
                  </AdminLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/activities/new"
              element={
                <ProtectedRoute>
                  <AdminLayout>
                    <ActivityForm mode="create" />
                  </AdminLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/activities/edit/:id"
              element={
                <ProtectedRoute>
                  <AdminLayout>
                    <ActivityForm mode="edit" />
                  </AdminLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/bookings"
              element={
                <ProtectedRoute>
                  <AdminLayout>
                    <BookingsAdmin />
                  </AdminLayout>
                </ProtectedRoute>
              }
            />
            <Route
              path="/admin/settings"
              element={
                <ProtectedRoute>
                  <AdminLayout>
                    <SettingsAdmin />
                  </AdminLayout>
                </ProtectedRoute>
              }
            />

            {/* Catch-all route for 404 errors */}
            <Route path="*" element={<NotFound />} />
          </Routes>
          <Toaster
            position="top-right"
            toastOptions={{
              style: {
                background: "white",
                border: "1px solid #e5e7eb",
                color: "#374151",
              },
            }}
          />
        </Router>
      </AuthProvider>
    </ErrorBoundary>
  );
}

export default App;
