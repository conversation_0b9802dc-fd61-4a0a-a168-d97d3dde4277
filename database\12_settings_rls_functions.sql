-- =====================================================
-- Malombo Admin Panel - Settings RLS Functions
-- =====================================================
-- Additional RLS policies and helper functions for settings

-- Function to get public settings (for frontend website)
CREATE OR REPLACE FUNCTION get_public_settings()
RETURNS TABLE (
    key TEXT,
    value JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT s.key, s.value
    FROM public.settings s
    WHERE s.is_public = true
    ORDER BY s.category, s.key;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get all settings (admin only)
CREATE OR REPLACE FUNCTION get_all_settings()
RETURNS TABLE (
    key TEXT,
    value JSONB,
    description TEXT,
    category TEXT,
    is_public BOOLEAN,
    updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    -- Check if user is admin
    IF NOT EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE profiles.id = auth.uid()
        AND profiles.role = 'admin'
        AND profiles.is_active = true
    ) THEN
        RAISE EXCEPTION 'Access denied: Admin role required';
    END IF;

    RETURN QUERY
    SELECT s.key, s.value, s.description, s.category, s.is_public, s.updated_at
    FROM public.settings s
    ORDER BY s.category, s.key;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get setting value by key
CREATE OR REPLACE FUNCTION get_setting_value(setting_key TEXT)
RETURNS JSONB AS $$
DECLARE
    setting_value JSONB;
    is_setting_public BOOLEAN;
BEGIN
    -- Get the setting and check if it's public
    SELECT s.value, s.is_public 
    INTO setting_value, is_setting_public
    FROM public.settings s
    WHERE s.key = setting_key;

    -- If setting doesn't exist, return null
    IF setting_value IS NULL THEN
        RETURN NULL;
    END IF;

    -- If setting is public, anyone can access it
    IF is_setting_public THEN
        RETURN setting_value;
    END IF;

    -- For private settings, check if user has access
    IF NOT EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE profiles.id = auth.uid()
        AND profiles.role IN ('admin', 'staff')
        AND profiles.is_active = true
    ) THEN
        RAISE EXCEPTION 'Access denied: Setting is private';
    END IF;

    RETURN setting_value;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to bulk update settings
CREATE OR REPLACE FUNCTION bulk_update_settings(
    settings_data JSONB
)
RETURNS INTEGER AS $$
DECLARE
    setting_record RECORD;
    updated_count INTEGER := 0;
    user_id UUID;
BEGIN
    -- Check if user is admin
    IF NOT EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE profiles.id = auth.uid()
        AND profiles.role = 'admin'
        AND profiles.is_active = true
    ) THEN
        RAISE EXCEPTION 'Access denied: Admin role required';
    END IF;

    user_id := auth.uid();

    -- Loop through each setting in the JSON object
    FOR setting_record IN 
        SELECT key, value 
        FROM jsonb_each(settings_data)
    LOOP
        -- Update or insert each setting
        INSERT INTO public.settings (key, value, updated_by)
        VALUES (setting_record.key, setting_record.value, user_id)
        ON CONFLICT (key) 
        DO UPDATE SET 
            value = EXCLUDED.value,
            updated_by = EXCLUDED.updated_by,
            updated_at = NOW();
        
        updated_count := updated_count + 1;
    END LOOP;

    RETURN updated_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to reset settings to defaults
CREATE OR REPLACE FUNCTION reset_settings_to_defaults()
RETURNS BOOLEAN AS $$
BEGIN
    -- Check if user is admin
    IF NOT EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE profiles.id = auth.uid()
        AND profiles.role = 'admin'
        AND profiles.is_active = true
    ) THEN
        RAISE EXCEPTION 'Access denied: Admin role required';
    END IF;

    -- Delete all existing settings
    DELETE FROM public.settings;

    -- Re-run the default settings insert
    -- (This would typically be done by re-running the settings creation script)
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to validate email template
CREATE OR REPLACE FUNCTION validate_email_template(
    template_name TEXT,
    template_data JSONB
)
RETURNS BOOLEAN AS $$
DECLARE
    required_fields TEXT[] := ARRAY['subject', 'html_body'];
    field TEXT;
BEGIN
    -- Check if all required fields are present
    FOREACH field IN ARRAY required_fields
    LOOP
        IF NOT (template_data ? field) THEN
            RAISE EXCEPTION 'Missing required field in email template: %', field;
        END IF;
        
        IF template_data->>field IS NULL OR template_data->>field = '' THEN
            RAISE EXCEPTION 'Empty value for required field: %', field;
        END IF;
    END LOOP;

    -- Validate that HTML body contains basic structure
    IF NOT (template_data->>'html_body' LIKE '%<html>%' AND 
            template_data->>'html_body' LIKE '%</html>%') THEN
        RAISE EXCEPTION 'Email template HTML body must contain valid HTML structure';
    END IF;

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Function to update email template with validation
CREATE OR REPLACE FUNCTION update_email_template(
    template_name TEXT,
    template_data JSONB
)
RETURNS BOOLEAN AS $$
DECLARE
    current_templates JSONB;
    updated_templates JSONB;
BEGIN
    -- Check if user is admin
    IF NOT EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE profiles.id = auth.uid()
        AND profiles.role = 'admin'
        AND profiles.is_active = true
    ) THEN
        RAISE EXCEPTION 'Access denied: Admin role required';
    END IF;

    -- Validate the template
    PERFORM validate_email_template(template_name, template_data);

    -- Get current email templates
    SELECT value INTO current_templates
    FROM public.settings
    WHERE key = 'email_templates';

    -- Update the specific template
    updated_templates := current_templates || jsonb_build_object(template_name, template_data);

    -- Save back to settings
    UPDATE public.settings
    SET value = updated_templates,
        updated_by = auth.uid()
    WHERE key = 'email_templates';

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get contact information for notifications
CREATE OR REPLACE FUNCTION get_notification_contacts(
    notification_type TEXT DEFAULT 'new_booking'
)
RETURNS TEXT[] AS $$
DECLARE
    email_settings JSONB;
    contacts TEXT[];
BEGIN
    -- Get email settings
    SELECT value INTO email_settings
    FROM public.settings
    WHERE key = 'email_settings';

    IF email_settings IS NULL THEN
        RETURN ARRAY[]::TEXT[];
    END IF;

    -- Extract contacts for the specific notification type
    SELECT ARRAY(
        SELECT jsonb_array_elements_text(
            email_settings->'booking_notifications'->notification_type
        )
    ) INTO contacts;

    RETURN COALESCE(contacts, ARRAY[]::TEXT[]);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get pricing configuration
CREATE OR REPLACE FUNCTION get_pricing_config()
RETURNS JSONB AS $$
DECLARE
    pricing_config JSONB;
BEGIN
    SELECT value INTO pricing_config
    FROM public.settings
    WHERE key = 'pricing_defaults';

    RETURN COALESCE(pricing_config, '{}'::JSONB);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to calculate taxes and fees
CREATE OR REPLACE FUNCTION calculate_booking_totals(
    base_amount NUMERIC,
    currency_code TEXT DEFAULT 'USD'
)
RETURNS TABLE (
    base_amount_out NUMERIC,
    tax_amount NUMERIC,
    service_charge NUMERIC,
    total_amount NUMERIC,
    tax_rate NUMERIC,
    service_rate NUMERIC
) AS $$
DECLARE
    pricing_config JSONB;
    tax_rate_val NUMERIC;
    service_rate_val NUMERIC;
BEGIN
    -- Get pricing configuration
    pricing_config := get_pricing_config();

    -- Extract rates (default to 0 if not found)
    tax_rate_val := COALESCE((pricing_config->>'tax_rate')::NUMERIC, 0) / 100.0;
    service_rate_val := COALESCE((pricing_config->>'service_charge')::NUMERIC, 0) / 100.0;

    -- Calculate amounts
    RETURN QUERY
    SELECT 
        base_amount as base_amount_out,
        ROUND(base_amount * tax_rate_val, 2) as tax_amount,
        ROUND(base_amount * service_rate_val, 2) as service_charge,
        ROUND(base_amount * (1 + tax_rate_val + service_rate_val), 2) as total_amount,
        tax_rate_val * 100 as tax_rate,
        service_rate_val * 100 as service_rate;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION get_public_settings() TO anon, authenticated;
GRANT EXECUTE ON FUNCTION get_all_settings() TO authenticated;
GRANT EXECUTE ON FUNCTION get_setting_value(TEXT) TO anon, authenticated;
GRANT EXECUTE ON FUNCTION bulk_update_settings(JSONB) TO authenticated;
GRANT EXECUTE ON FUNCTION reset_settings_to_defaults() TO authenticated;
GRANT EXECUTE ON FUNCTION validate_email_template(TEXT, JSONB) TO authenticated;
GRANT EXECUTE ON FUNCTION update_email_template(TEXT, JSONB) TO authenticated;
GRANT EXECUTE ON FUNCTION get_notification_contacts(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION get_pricing_config() TO authenticated;
GRANT EXECUTE ON FUNCTION calculate_booking_totals(NUMERIC, TEXT) TO authenticated;

COMMENT ON FUNCTION get_public_settings IS 'Get all public settings for frontend display';
COMMENT ON FUNCTION get_all_settings IS 'Get all settings (admin only)';
COMMENT ON FUNCTION get_setting_value IS 'Get a specific setting value with access control';
COMMENT ON FUNCTION bulk_update_settings IS 'Update multiple settings at once (admin only)';
COMMENT ON FUNCTION update_email_template IS 'Update email template with validation';
COMMENT ON FUNCTION get_notification_contacts IS 'Get email contacts for specific notification types';
COMMENT ON FUNCTION calculate_booking_totals IS 'Calculate taxes and total amounts based on pricing settings';
