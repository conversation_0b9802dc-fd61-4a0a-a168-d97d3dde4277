# Malombo Activities Management System

## Overview

The Activities Management System is a comprehensive CRUD interface for managing activity listings in the Malombo Selous Forest Camp admin panel. This system provides full functionality for creating, editing, deleting, and managing activities with image uploads, status controls, and public site integration.

## ✅ Features Implemented

### Database & Storage
- **Activities Table**: Complete schema with all required fields (title, category, description, duration, schedule, pricing, inclusions, images, status, featured)
- **Supabase Storage**: Dedicated bucket for activity images (`activity-images`)
- **Row Level Security**: Proper access control for admin/staff users
- **Data Validation**: Server-side validation with triggers
- **Helper Functions**: Bulk operations, search functionality, and statistics

### Admin Panel Features
- **Activities List Page**: Table view with search, filters, pagination, and sorting
- **Add/Edit Activity Form**: Comprehensive form with validation and image upload
- **Status Management**: Draft, Published, Unpublished states
- **Featured Toggle**: Highlight activities for homepage display
- **Image Management**: Multiple image upload with preview and removal
- **Category System**: Predefined categories (Game Drive, Boat Safari, Walking Safari, etc.)
- **Inclusions Management**: Multi-select inclusions with predefined options
- **Bulk Operations**: Mass status updates and featured toggles

### Public Site Integration
- **Published Activities Only**: Only published activities appear on public site
- **Featured Activities**: Priority display for featured activities
- **Category Filtering**: Activities organized by category
- **Search Functionality**: Full-text search across title and description

## 📁 File Structure

```
database/
├── 07_create_activities_table.sql    # Activities table creation
└── 08_activities_rls_functions.sql   # RLS policies & helper functions

src/
├── types/
│   └── activity.ts                   # TypeScript definitions
├── lib/
│   └── activities.ts                 # Service layer functions
└── pages/admin/
    ├── ActivitiesAdmin.tsx           # Activities list page
    └── ActivityForm.tsx              # Add/edit form component
```

## 🚀 Setup Instructions

### 1. Database Setup

Run the SQL scripts in order:

```sql
-- 1. Create activities table and storage bucket
\i database/07_create_activities_table.sql

-- 2. Set up RLS policies and helper functions
\i database/08_activities_rls_functions.sql
```

### 2. Environment Variables

Ensure your `.env` file includes:

```env
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 3. Dependencies

The following dependencies are required (already installed):

```json
{
  "react-hook-form": "^7.62.0",
  "sonner": "^2.0.7"
}
```

## 🎯 Usage Guide

### Admin Panel Access

1. **Navigate to Activities**: `/admin/activities`
2. **Create New**: Click "Add Activity" button
3. **Edit Existing**: Click edit icon in table row
4. **Delete**: Click delete icon with confirmation
5. **Toggle Status**: Use eye/eye-off icons for publish/unpublish
6. **Toggle Featured**: Use star icons for featured status

### Form Fields

#### Required Fields
- **Title**: Activity name (3-200 characters)
- **Category**: Select from predefined categories
- **Description**: Detailed description (10-5000 characters)
- **Duration**: Activity duration (e.g., "3 hours", "Full Day")
- **Pricing**: Price information (e.g., "$50 per person")
- **Images**: At least 1 image required (max 10, 10MB each)

#### Optional Fields
- **Schedule**: Time slots (e.g., "Morning (6AM–9AM)")
- **Inclusions**: What's included (guide, equipment, meals, etc.)
- **Featured**: Highlight on homepage
- **Status**: Draft, Published, or Unpublished

### Activity Categories

- **Game Drive**: Traditional safari game drives
- **Boat Safari**: River-based wildlife viewing
- **Walking Safari**: On-foot nature experiences
- **Cultural Tour**: Local culture and traditions
- **Fishing**: Angling experiences
- **Village Tour**: Community visits
- **Youth Program**: Activities for young visitors

### Status Management

- **Draft**: Work in progress, not visible to public
- **Published**: Live on public website
- **Unpublished**: Temporarily hidden from public

### Featured Activities

- Featured activities appear prominently on homepage
- Prioritized in search results and category listings
- Use sparingly for best experiences

## 🔧 Technical Details

### Database Schema

```sql
CREATE TABLE public.activities (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    category TEXT NOT NULL CHECK (category IN (...)),
    description TEXT NOT NULL,
    duration TEXT NOT NULL,
    schedule TEXT,
    pricing TEXT NOT NULL,
    inclusions JSONB DEFAULT '[]'::jsonb,
    images JSONB DEFAULT '[]'::jsonb,
    status TEXT NOT NULL CHECK (status IN ('draft', 'published', 'unpublished')) DEFAULT 'draft',
    featured BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES auth.users(id),
    updated_by UUID REFERENCES auth.users(id)
);
```

### API Functions

- `getActivities()`: List with filtering, sorting, pagination
- `getActivity(id)`: Get single activity
- `createActivity(data)`: Create new activity
- `updateActivity(data)`: Update existing activity
- `deleteActivity(id)`: Delete activity and images
- `uploadActivityImage()`: Upload to Supabase storage
- `updateActivityStatus()`: Change publication status
- `toggleActivityFeatured()`: Toggle featured status

### Validation Rules

- **Title**: 3-200 characters, required
- **Description**: 10-5000 characters, required
- **Images**: 1-10 images, max 10MB each, JPG/PNG/GIF/WebP
- **Category**: Must be from predefined list
- **Duration**: Required
- **Pricing**: Required

## 🔒 Security Features

- **Row Level Security**: Users can only access data based on their role
- **Image Upload Security**: Validated file types and sizes
- **Input Validation**: Server-side validation for all fields
- **CSRF Protection**: Built into Supabase authentication
- **Role-based Access**: Admin vs Staff permissions

## 🌐 Public Site Integration

### Published Activities View

```sql
CREATE VIEW public.activities_public AS
SELECT id, title, category, description, duration, schedule, 
       pricing, inclusions, images, featured, created_at
FROM public.activities
WHERE status = 'published'
ORDER BY featured DESC, created_at DESC;
```

### Usage in Public Site

```typescript
// Get all published activities
const activities = await getPublishedActivities();

// Get featured activities for homepage
const featured = await getFeaturedActivities();

// Get activities by category
const gamedrives = await getActivitiesByCategory('game_drive');

// Search activities
const results = await searchPublishedActivities('safari');
```

## 📊 Analytics & Reporting

The system includes helper functions for activity statistics:

- Total activities count
- Published vs draft activities
- Featured activities count
- Activities by category breakdown

## 🎨 Styling & Theme

- Consistent safari-inspired theme (earthy browns, greens, golden highlights)
- Responsive design for all screen sizes
- Category-specific color coding
- Status badges with appropriate colors
- Loading states and animations

## 🚨 Error Handling

- Comprehensive error messages
- Toast notifications for user feedback
- Graceful fallbacks for failed operations
- Image upload error handling
- Form validation with inline errors

## 📝 Sample Data

The setup includes sample activities for testing:

1. **Game Drive – Morning Safari** (Featured)
2. **Sunset Boat Safari** (Featured)
3. **Walking Safari Adventure**
4. **Cultural Village Tour**
5. **Fishing Expedition** (Draft)

## 🔄 Next Steps

1. **Test all CRUD operations** in the admin panel
2. **Upload sample images** for activities
3. **Verify public site integration** with published activities
4. **Set up email notifications** for activity bookings (future enhancement)
5. **Add activity booking system** (future enhancement)

## ✅ Completion Checklist

- [x] Database tables and storage created
- [x] TypeScript types and interfaces defined
- [x] Service layer functions implemented
- [x] Activities list page with full functionality
- [x] Add/edit form with validation and image upload
- [x] Routing and navigation updated
- [x] Status and featured management
- [x] Public site integration prepared
- [x] Documentation completed

The Activities Management system is now fully functional and ready for use!
