-- =====================================================
-- Accommodations RLS Policies and Helper Functions
-- =====================================================
-- Additional RLS policies and helper functions for accommodations management

-- Create helper function to check if user is admin
CREATE OR REPLACE FUNCTION public.is_admin(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE profiles.id = user_id 
        AND profiles.role = 'admin'
        AND profiles.is_active = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create helper function to check if user is staff or admin
CREATE OR REPLACE FUNCTION public.is_staff_or_admin(user_id UUID DEFAULT auth.uid())
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE profiles.id = user_id 
        AND profiles.role IN ('admin', 'staff')
        AND profiles.is_active = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to validate accommodation data
CREATE OR REPLACE FUNCTION public.validate_accommodation_data()
RETURNS TRIGGER AS $$
BEGIN
    -- Validate required fields
    IF NEW.name IS NULL OR trim(NEW.name) = '' THEN
        RAISE EXCEPTION 'Accommodation name is required';
    END IF;

    IF NEW.type IS NULL OR trim(NEW.type) = '' THEN
        RAISE EXCEPTION 'Accommodation type is required';
    END IF;

    IF NEW.description IS NULL OR trim(NEW.description) = '' THEN
        RAISE EXCEPTION 'Accommodation description is required';
    END IF;

    IF NEW.price_range IS NULL OR trim(NEW.price_range) = '' THEN
        RAISE EXCEPTION 'Price range is required';
    END IF;

    IF NEW.capacity IS NULL OR NEW.capacity < 1 THEN
        RAISE EXCEPTION 'Capacity must be at least 1';
    END IF;

    -- Validate type enum
    IF NEW.type NOT IN ('banda', 'room', 'tent', 'tree_house', 'campsite', 'suite', 'lodge', 'cabin') THEN
        RAISE EXCEPTION 'Invalid accommodation type: %', NEW.type;
    END IF;

    -- Validate status enum
    IF NEW.status NOT IN ('draft', 'published', 'unpublished') THEN
        RAISE EXCEPTION 'Invalid status: %', NEW.status;
    END IF;

    -- Validate amenities is a valid JSON array
    IF NEW.amenities IS NOT NULL THEN
        BEGIN
            -- Try to access the JSON array to validate it
            PERFORM jsonb_array_length(NEW.amenities);
        EXCEPTION WHEN OTHERS THEN
            RAISE EXCEPTION 'Amenities must be a valid JSON array';
        END;
    END IF;

    -- Validate images is a valid JSON array
    IF NEW.images IS NOT NULL THEN
        BEGIN
            -- Try to access the JSON array to validate it
            PERFORM jsonb_array_length(NEW.images);
        EXCEPTION WHEN OTHERS THEN
            RAISE EXCEPTION 'Images must be a valid JSON array';
        END;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for accommodation validation
DROP TRIGGER IF EXISTS trigger_validate_accommodation_data ON public.accommodations;
CREATE TRIGGER trigger_validate_accommodation_data
    BEFORE INSERT OR UPDATE ON public.accommodations
    FOR EACH ROW
    EXECUTE FUNCTION public.validate_accommodation_data();

-- Create function to get accommodation statistics
CREATE OR REPLACE FUNCTION public.get_accommodation_stats()
RETURNS JSON AS $$
DECLARE
    result JSON;
BEGIN
    SELECT json_build_object(
        'total', COUNT(*),
        'published', COUNT(*) FILTER (WHERE status = 'published'),
        'draft', COUNT(*) FILTER (WHERE status = 'draft'),
        'unpublished', COUNT(*) FILTER (WHERE status = 'unpublished'),
        'featured', COUNT(*) FILTER (WHERE featured = true),
        'by_type', (
            SELECT json_object_agg(type, count)
            FROM (
                SELECT type, COUNT(*) as count
                FROM public.accommodations
                GROUP BY type
            ) type_counts
        )
    ) INTO result
    FROM public.accommodations;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to search accommodations with filters
CREATE OR REPLACE FUNCTION public.search_accommodations(
    search_term TEXT DEFAULT '',
    filter_type TEXT DEFAULT 'all',
    filter_status TEXT DEFAULT 'all',
    filter_featured BOOLEAN DEFAULT NULL,
    sort_field TEXT DEFAULT 'created_at',
    sort_direction TEXT DEFAULT 'desc',
    page_limit INTEGER DEFAULT 10,
    page_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    accommodation_data JSON,
    total_count BIGINT
) AS $$
DECLARE
    base_query TEXT;
    count_query TEXT;
    where_conditions TEXT[] := ARRAY[]::TEXT[];
    order_clause TEXT;
    total_records BIGINT;
BEGIN
    -- Build WHERE conditions
    IF search_term IS NOT NULL AND trim(search_term) != '' THEN
        where_conditions := array_append(where_conditions, 
            format('(name ILIKE %L OR description ILIKE %L)', 
                   '%' || search_term || '%', '%' || search_term || '%'));
    END IF;

    IF filter_type IS NOT NULL AND filter_type != 'all' THEN
        where_conditions := array_append(where_conditions, 
            format('type = %L', filter_type));
    END IF;

    IF filter_status IS NOT NULL AND filter_status != 'all' THEN
        where_conditions := array_append(where_conditions, 
            format('status = %L', filter_status));
    END IF;

    IF filter_featured IS NOT NULL THEN
        where_conditions := array_append(where_conditions, 
            format('featured = %L', filter_featured));
    END IF;

    -- Build ORDER BY clause
    IF sort_field IN ('name', 'type', 'price_range', 'capacity', 'created_at', 'updated_at') THEN
        IF sort_direction = 'asc' THEN
            order_clause := format('ORDER BY %I ASC', sort_field);
        ELSE
            order_clause := format('ORDER BY %I DESC', sort_field);
        END IF;
    ELSE
        order_clause := 'ORDER BY created_at DESC';
    END IF;

    -- Build base query
    base_query := 'SELECT to_json(accommodations.*) FROM public.accommodations';
    count_query := 'SELECT COUNT(*) FROM public.accommodations';

    -- Add WHERE clause if conditions exist
    IF array_length(where_conditions, 1) > 0 THEN
        base_query := base_query || ' WHERE ' || array_to_string(where_conditions, ' AND ');
        count_query := count_query || ' WHERE ' || array_to_string(where_conditions, ' AND ');
    END IF;

    -- Get total count
    EXECUTE count_query INTO total_records;

    -- Add ORDER BY and LIMIT/OFFSET
    base_query := base_query || ' ' || order_clause || 
                  format(' LIMIT %s OFFSET %s', page_limit, page_offset);

    -- Return results
    RETURN QUERY EXECUTE format('
        SELECT accommodation_json, %L::BIGINT as total_count
        FROM (%s) as subquery(accommodation_json)
    ', total_records, base_query);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to bulk update accommodation status
CREATE OR REPLACE FUNCTION public.bulk_update_accommodation_status(
    accommodation_ids UUID[],
    new_status TEXT
)
RETURNS JSON AS $$
DECLARE
    updated_count INTEGER;
    result JSON;
BEGIN
    -- Validate status
    IF new_status NOT IN ('draft', 'published', 'unpublished') THEN
        RAISE EXCEPTION 'Invalid status: %', new_status;
    END IF;

    -- Check if user is admin
    IF NOT public.is_admin() THEN
        RAISE EXCEPTION 'Only admins can perform bulk operations';
    END IF;

    -- Update accommodations
    UPDATE public.accommodations 
    SET status = new_status, updated_at = NOW(), updated_by = auth.uid()
    WHERE id = ANY(accommodation_ids);

    GET DIAGNOSTICS updated_count = ROW_COUNT;

    -- Return result
    SELECT json_build_object(
        'success', true,
        'updated_count', updated_count,
        'message', format('%s accommodations updated to %s', updated_count, new_status)
    ) INTO result;

    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to bulk update featured status
CREATE OR REPLACE FUNCTION public.bulk_update_accommodation_featured(
    accommodation_ids UUID[],
    is_featured BOOLEAN
)
RETURNS JSON AS $$
DECLARE
    updated_count INTEGER;
    result JSON;
BEGIN
    -- Check if user is admin
    IF NOT public.is_admin() THEN
        RAISE EXCEPTION 'Only admins can perform bulk operations';
    END IF;

    -- Update accommodations
    UPDATE public.accommodations 
    SET featured = is_featured, updated_at = NOW(), updated_by = auth.uid()
    WHERE id = ANY(accommodation_ids);

    GET DIAGNOSTICS updated_count = ROW_COUNT;

    -- Return result
    SELECT json_build_object(
        'success', true,
        'updated_count', updated_count,
        'message', format('%s accommodations %s', updated_count, 
                         CASE WHEN is_featured THEN 'featured' ELSE 'unfeatured' END)
    ) INTO result;

    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION public.is_admin(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_staff_or_admin(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_accommodation_stats() TO authenticated;
GRANT EXECUTE ON FUNCTION public.search_accommodations(TEXT, TEXT, TEXT, BOOLEAN, TEXT, TEXT, INTEGER, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION public.bulk_update_accommodation_status(UUID[], TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.bulk_update_accommodation_featured(UUID[], BOOLEAN) TO authenticated;

-- Create indexes for better search performance
CREATE INDEX IF NOT EXISTS idx_accommodations_search_name ON public.accommodations USING gin(to_tsvector('english', name));
CREATE INDEX IF NOT EXISTS idx_accommodations_search_description ON public.accommodations USING gin(to_tsvector('english', description));
CREATE INDEX IF NOT EXISTS idx_accommodations_type_status ON public.accommodations(type, status);
CREATE INDEX IF NOT EXISTS idx_accommodations_featured_status ON public.accommodations(featured, status);

-- Success message
DO $$
BEGIN
    RAISE NOTICE 'Accommodations RLS policies and helper functions created successfully!';
    RAISE NOTICE 'Available functions:';
    RAISE NOTICE '- is_admin(user_id)';
    RAISE NOTICE '- is_staff_or_admin(user_id)';
    RAISE NOTICE '- get_accommodation_stats()';
    RAISE NOTICE '- search_accommodations(...)';
    RAISE NOTICE '- bulk_update_accommodation_status(ids, status)';
    RAISE NOTICE '- bulk_update_accommodation_featured(ids, featured)';
END $$;
