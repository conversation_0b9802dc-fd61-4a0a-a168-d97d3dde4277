-- =====================================================
-- Malombo Admin Panel - Activities RLS Functions
-- =====================================================
-- Helper functions and additional RLS policies for activities management

-- Function to check if user is admin
CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE profiles.id = auth.uid() 
        AND profiles.role = 'admin'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is staff or admin
CREATE OR REPLACE FUNCTION public.is_staff_or_admin()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE profiles.id = auth.uid() 
        AND profiles.role IN ('admin', 'staff')
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get activities with search and filters
CREATE OR REPLACE FUNCTION public.search_activities(
    search_term TEXT DEFAULT NULL,
    filter_category TEXT DEFAULT NULL,
    filter_status TEXT DEFAULT NULL,
    filter_featured BOOLEAN DEFAULT NULL,
    sort_field TEXT DEFAULT 'created_at',
    sort_direction TEXT DEFAULT 'desc',
    page_limit INTEGER DEFAULT 10,
    page_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    title TEXT,
    category TEXT,
    description TEXT,
    duration TEXT,
    schedule TEXT,
    pricing TEXT,
    inclusions JSONB,
    images JSONB,
    status TEXT,
    featured BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    total_count BIGINT
) AS $$
DECLARE
    query_text TEXT;
    count_query TEXT;
    total_records BIGINT;
BEGIN
    -- Build the base query
    query_text := 'SELECT a.id, a.title, a.category, a.description, a.duration, a.schedule, 
                          a.pricing, a.inclusions, a.images, a.status, a.featured, 
                          a.created_at, a.updated_at
                   FROM public.activities a WHERE 1=1';
    
    -- Add search filter
    IF search_term IS NOT NULL AND search_term != '' THEN
        query_text := query_text || ' AND (a.title ILIKE ''%' || search_term || '%'' 
                                          OR a.description ILIKE ''%' || search_term || '%'')';
    END IF;
    
    -- Add category filter
    IF filter_category IS NOT NULL AND filter_category != 'all' THEN
        query_text := query_text || ' AND a.category = ''' || filter_category || '''';
    END IF;
    
    -- Add status filter
    IF filter_status IS NOT NULL AND filter_status != 'all' THEN
        query_text := query_text || ' AND a.status = ''' || filter_status || '''';
    END IF;
    
    -- Add featured filter
    IF filter_featured IS NOT NULL THEN
        query_text := query_text || ' AND a.featured = ' || filter_featured;
    END IF;
    
    -- Get total count
    count_query := 'SELECT COUNT(*) FROM public.activities a WHERE 1=1';
    
    IF search_term IS NOT NULL AND search_term != '' THEN
        count_query := count_query || ' AND (a.title ILIKE ''%' || search_term || '%'' 
                                            OR a.description ILIKE ''%' || search_term || '%'')';
    END IF;
    
    IF filter_category IS NOT NULL AND filter_category != 'all' THEN
        count_query := count_query || ' AND a.category = ''' || filter_category || '''';
    END IF;
    
    IF filter_status IS NOT NULL AND filter_status != 'all' THEN
        count_query := count_query || ' AND a.status = ''' || filter_status || '''';
    END IF;
    
    IF filter_featured IS NOT NULL THEN
        count_query := count_query || ' AND a.featured = ' || filter_featured;
    END IF;
    
    EXECUTE count_query INTO total_records;
    
    -- Add sorting
    IF sort_field = 'title' THEN
        query_text := query_text || ' ORDER BY a.title';
    ELSIF sort_field = 'category' THEN
        query_text := query_text || ' ORDER BY a.category';
    ELSIF sort_field = 'pricing' THEN
        query_text := query_text || ' ORDER BY a.pricing';
    ELSIF sort_field = 'status' THEN
        query_text := query_text || ' ORDER BY a.status';
    ELSIF sort_field = 'featured' THEN
        query_text := query_text || ' ORDER BY a.featured';
    ELSE
        query_text := query_text || ' ORDER BY a.created_at';
    END IF;
    
    IF sort_direction = 'asc' THEN
        query_text := query_text || ' ASC';
    ELSE
        query_text := query_text || ' DESC';
    END IF;
    
    -- Add pagination
    query_text := query_text || ' LIMIT ' || page_limit || ' OFFSET ' || page_offset;
    
    -- Return results with total count
    RETURN QUERY EXECUTE 'SELECT *, ' || total_records || '::BIGINT as total_count FROM (' || query_text || ') subq';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to bulk update activity status
CREATE OR REPLACE FUNCTION public.bulk_update_activity_status(
    activity_ids UUID[],
    new_status TEXT
)
RETURNS TABLE (
    success BOOLEAN,
    updated_count INTEGER,
    message TEXT
) AS $$
DECLARE
    updated_rows INTEGER;
BEGIN
    -- Check if user has permission
    IF NOT public.is_staff_or_admin() THEN
        RETURN QUERY SELECT FALSE, 0, 'Insufficient permissions'::TEXT;
        RETURN;
    END IF;
    
    -- Validate status
    IF new_status NOT IN ('draft', 'published', 'unpublished') THEN
        RETURN QUERY SELECT FALSE, 0, 'Invalid status value'::TEXT;
        RETURN;
    END IF;
    
    -- Update activities
    UPDATE public.activities 
    SET status = new_status, updated_at = NOW(), updated_by = auth.uid()
    WHERE id = ANY(activity_ids);
    
    GET DIAGNOSTICS updated_rows = ROW_COUNT;
    
    RETURN QUERY SELECT TRUE, updated_rows, 'Activities updated successfully'::TEXT;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to bulk update activity featured status
CREATE OR REPLACE FUNCTION public.bulk_update_activity_featured(
    activity_ids UUID[],
    is_featured BOOLEAN
)
RETURNS TABLE (
    success BOOLEAN,
    updated_count INTEGER,
    message TEXT
) AS $$
DECLARE
    updated_rows INTEGER;
BEGIN
    -- Check if user has permission
    IF NOT public.is_staff_or_admin() THEN
        RETURN QUERY SELECT FALSE, 0, 'Insufficient permissions'::TEXT;
        RETURN;
    END IF;
    
    -- Update activities
    UPDATE public.activities 
    SET featured = is_featured, updated_at = NOW(), updated_by = auth.uid()
    WHERE id = ANY(activity_ids);
    
    GET DIAGNOSTICS updated_rows = ROW_COUNT;
    
    RETURN QUERY SELECT TRUE, updated_rows, 'Activities updated successfully'::TEXT;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get activity statistics
CREATE OR REPLACE FUNCTION public.get_activity_stats()
RETURNS TABLE (
    total_activities BIGINT,
    published_activities BIGINT,
    draft_activities BIGINT,
    featured_activities BIGINT,
    activities_by_category JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        (SELECT COUNT(*) FROM public.activities) as total_activities,
        (SELECT COUNT(*) FROM public.activities WHERE status = 'published') as published_activities,
        (SELECT COUNT(*) FROM public.activities WHERE status = 'draft') as draft_activities,
        (SELECT COUNT(*) FROM public.activities WHERE featured = true) as featured_activities,
        (SELECT jsonb_object_agg(category, count) 
         FROM (
             SELECT category, COUNT(*) as count 
             FROM public.activities 
             GROUP BY category
         ) cat_counts) as activities_by_category;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to validate activity data
CREATE OR REPLACE FUNCTION public.validate_activity_data()
RETURNS TRIGGER AS $$
BEGIN
    -- Validate title length
    IF LENGTH(TRIM(NEW.title)) < 3 THEN
        RAISE EXCEPTION 'Activity title must be at least 3 characters long';
    END IF;
    
    IF LENGTH(TRIM(NEW.title)) > 200 THEN
        RAISE EXCEPTION 'Activity title must be less than 200 characters';
    END IF;
    
    -- Validate description length
    IF LENGTH(TRIM(NEW.description)) < 10 THEN
        RAISE EXCEPTION 'Activity description must be at least 10 characters long';
    END IF;
    
    IF LENGTH(TRIM(NEW.description)) > 5000 THEN
        RAISE EXCEPTION 'Activity description must be less than 5000 characters';
    END IF;
    
    -- Validate duration
    IF LENGTH(TRIM(NEW.duration)) < 1 THEN
        RAISE EXCEPTION 'Activity duration is required';
    END IF;
    
    -- Validate pricing
    IF LENGTH(TRIM(NEW.pricing)) < 1 THEN
        RAISE EXCEPTION 'Activity pricing is required';
    END IF;
    
    -- Validate inclusions is valid JSON array
    IF NEW.inclusions IS NOT NULL AND jsonb_typeof(NEW.inclusions) != 'array' THEN
        RAISE EXCEPTION 'Inclusions must be a JSON array';
    END IF;
    
    -- Validate images is valid JSON array
    IF NEW.images IS NOT NULL AND jsonb_typeof(NEW.images) != 'array' THEN
        RAISE EXCEPTION 'Images must be a JSON array';
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create validation trigger
CREATE TRIGGER trigger_validate_activity_data
    BEFORE INSERT OR UPDATE ON public.activities
    FOR EACH ROW
    EXECUTE FUNCTION public.validate_activity_data();

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION public.is_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_staff_or_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION public.search_activities(TEXT, TEXT, TEXT, BOOLEAN, TEXT, TEXT, INTEGER, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION public.bulk_update_activity_status(UUID[], TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.bulk_update_activity_featured(UUID[], BOOLEAN) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_activity_stats() TO authenticated;

-- Success message
DO $$
BEGIN
    RAISE NOTICE 'Activities RLS functions and triggers created successfully!';
    RAISE NOTICE 'Helper functions for search, bulk operations, and validation are ready.';
END $$;
