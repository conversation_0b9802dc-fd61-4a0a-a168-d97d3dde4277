// =====================================================
// Settings Types - Comprehensive TypeScript Definitions
// =====================================================
// Type definitions for the settings management system

// Base setting structure
export interface Setting {
  key: string;
  value: any; // JSON value
  description?: string;
  category: SettingCategory;
  is_public: boolean;
  created_at: string;
  updated_at: string;
  updated_by?: string;
}

// Setting categories
export type SettingCategory =
  | "contacts"
  | "policies"
  | "pricing"
  | "email"
  | "display"
  | "business"
  | "general";

// =====================================================
// Lodge Contacts Settings
// =====================================================

export interface LodgeContacts {
  emails: {
    reservations: string;
    sales: string;
    info: string;
    manager: string;
  };
  phones: string[];
  address: {
    street: string;
    city: string;
    country: string;
    postal_code: string;
  };
}

// =====================================================
// Policy Settings
// =====================================================

export interface CheckInPolicy {
  check_in_time: string; // HH:mm format
  check_in_end: string; // HH:mm format
  check_out_time: string; // HH:mm format
  late_checkout_fee: number;
  early_checkin_available: boolean;
  early_checkin_fee: number;
}

export interface ChildPricingPolicy {
  age_groups: {
    infant: {
      min_age: number;
      max_age: number;
      price_percentage: number;
      description: string;
    };
    child: {
      min_age: number;
      max_age: number;
      price_percentage: number;
      description: string;
    };
    adult: {
      min_age: number;
      max_age: number | null;
      price_percentage: number;
      description: string;
    };
  };
  max_children_per_room: number;
  infant_bed_required: boolean;
}

export interface CancellationPolicy {
  free_cancellation_days: number;
  partial_refund_days: number;
  partial_refund_percentage: number;
  no_refund_days: number;
  emergency_exceptions: boolean;
  policy_text: string;
}

// =====================================================
// Pricing Settings
// =====================================================

export interface DiscountPreset {
  name: string;
  percentage: number;
  description: string;
}

export interface PricingDefaults {
  default_currency: string;
  accepted_currencies: string[];
  tax_rate: number;
  service_charge: number;
  seasonal_pricing: boolean;
  discount_presets: DiscountPreset[];
}

// =====================================================
// Email Settings
// =====================================================

export interface EmailSettings {
  sender_name: string;
  sender_email: string;
  reply_to: string;
  default_cc: string[];
  default_bcc: string[];
  booking_notifications: {
    new_booking: string[];
    booking_confirmed: string[];
    booking_cancelled: string[];
    payment_received: string[];
  };
}

export interface EmailTemplate {
  subject: string;
  html_body: string;
}

export interface EmailTemplates {
  booking_confirmation: EmailTemplate;
  booking_update: EmailTemplate;
  booking_cancellation: EmailTemplate;
}

// =====================================================
// Display Settings
// =====================================================

export interface ThemeSettings {
  primary_color: string;
  secondary_color: string;
  accent_color: string;
  background_color: string;
  text_color: string;
}

export interface CurrencyFormat {
  symbol_position: "before" | "after";
  decimal_places: number;
  thousands_separator: string;
}

export interface DisplaySettings {
  theme: ThemeSettings;
  logo_url: string;
  favicon_url: string;
  default_images: {
    accommodation_placeholder: string;
    activity_placeholder: string;
  };
  date_format: string;
  time_format: string;
  currency_format: CurrencyFormat;
}

// =====================================================
// Business Information
// =====================================================

export interface BusinessInfo {
  name: string;
  tagline: string;
  description: string;
  established_year: number;
  license_number: string;
  social_media: {
    facebook: string;
    instagram: string;
    twitter: string;
  };
  certifications: string[];
}

// =====================================================
// Settings API Types
// =====================================================

// Settings response types
export interface SettingsResponse {
  data: Setting[];
  success: boolean;
  message?: string;
}

export interface SettingResponse {
  data: Setting;
  success: boolean;
  message?: string;
}

// Settings update request
export interface SettingsUpdateRequest {
  [key: string]: any;
}

// Settings by category response
export interface SettingsByCategory {
  [category: string]: Setting[];
}

// Bulk settings update
export interface BulkSettingsUpdate {
  settings: Record<string, any>;
}

// =====================================================
// Settings Form Types
// =====================================================

export interface SettingsFormState {
  contacts: LodgeContacts;
  checkInPolicy: CheckInPolicy;
  childPricingPolicy: ChildPricingPolicy;
  cancellationPolicy: CancellationPolicy;
  pricingDefaults: PricingDefaults;
  emailSettings: EmailSettings;
  emailTemplates: EmailTemplates;
  displaySettings: DisplaySettings;
  businessInfo: BusinessInfo;
  errors: SettingsValidationError[];
  isSubmitting: boolean;
  isDirty: boolean;
}

export interface SettingsValidationError {
  field: string;
  message: string;
  code: string;
}

// =====================================================
// Settings Configuration
// =====================================================

export const SETTING_CATEGORIES: Record<
  SettingCategory,
  {
    label: string;
    description: string;
    icon: string;
  }
> = {
  contacts: {
    label: "Lodge Contacts",
    description: "Email addresses, phone numbers, and physical address",
    icon: "Phone",
  },
  policies: {
    label: "Policies",
    description: "Check-in/out times, child pricing, and cancellation policies",
    icon: "FileText",
  },
  pricing: {
    label: "Pricing",
    description: "Default currency, tax rates, and discount presets",
    icon: "DollarSign",
  },
  email: {
    label: "Email",
    description: "Email configuration and notification templates",
    icon: "Mail",
  },
  display: {
    label: "Display",
    description: "Theme colors, logos, and formatting preferences",
    icon: "Palette",
  },
  business: {
    label: "Business Info",
    description: "Company information and certifications",
    icon: "Building",
  },
  general: {
    label: "General",
    description: "General system settings",
    icon: "Settings",
  },
};

// Default values for new settings
export const DEFAULT_SETTINGS: Record<string, any> = {
  lodge_contacts: {
    emails: {
      reservations: "<EMAIL>",
      sales: "<EMAIL>",
      info: "<EMAIL>",
      manager: "<EMAIL>",
    },
    phones: ["+*********** 901", "+*********** 104", "+*********** 555"],
    address: {
      street: "Selous Game Reserve",
      city: "Morogoro",
      country: "Tanzania",
      postal_code: "",
    },
  },
  check_in_policy: {
    check_in_time: "12:00",
    check_in_end: "18:00",
    check_out_time: "10:00",
    late_checkout_fee: 50,
    early_checkin_available: true,
    early_checkin_fee: 30,
  },
  child_pricing_policy: {
    age_groups: {
      infant: {
        min_age: 0,
        max_age: 4,
        price_percentage: 0,
        description: "Children 0-4 years stay free",
      },
      child: {
        min_age: 5,
        max_age: 13,
        price_percentage: 50,
        description: "Children 5-13 years pay 50% of adult rate",
      },
      adult: {
        min_age: 14,
        max_age: null,
        price_percentage: 100,
        description: "14+ years pay full adult rate",
      },
    },
    max_children_per_room: 3,
    infant_bed_required: false,
  },
  pricing_defaults: {
    default_currency: "TZS",
    accepted_currencies: ["TZS", "USD", "EUR", "GBP"],
    tax_rate: 18,
    service_charge: 0,
    seasonal_pricing: true,
    discount_presets: [
      {
        name: "Early Bird",
        percentage: 10,
        description: "Book 60+ days in advance",
      },
      { name: "Extended Stay", percentage: 15, description: "Stay 7+ nights" },
      {
        name: "Repeat Guest",
        percentage: 5,
        description: "Previous guest discount",
      },
      {
        name: "Group Booking",
        percentage: 12,
        description: "4+ rooms booked together",
      },
    ],
  },
};
