// =====================================================
// Database and Application Constants
// =====================================================

/**
 * Database field constraints and limits
 */
export const DATABASE_LIMITS = {
  // Current database schema uses NUMERIC(10,2)
  // Maximum value: 99,999,999.99 (8 digits before decimal, 2 after)
  BOOKING_AMOUNT_MAX: 99999999.99,
  
  // If you run the migration to increase precision to NUMERIC(12,2)
  // Uncomment the line below and comment the line above
  // BOOKING_AMOUNT_MAX: 9999999999.99,
  
  BOOKING_AMOUNT_MIN: 0,
  
  // Warning threshold (80% of max value)
  BOOKING_AMOUNT_WARNING_THRESHOLD: 99999999.99 * 0.8,
  
  // Decimal places for currency amounts
  CURRENCY_DECIMAL_PLACES: 2,
} as const;

/**
 * Currency configuration
 */
export const CURRENCY_LIMITS = {
  TZS: {
    // Typical TZS booking ranges
    TYPICAL_MIN: 10000,      // TZS 10,000
    TYPICAL_MAX: 50000000,   // TZS 50,000,000 (50M)
    WARNING_THRESHOLD: 80000000, // TZS 80,000,000 (80M)
  },
  USD: {
    TYPICAL_MIN: 50,
    TYPICAL_MAX: 25000,
    WARNING_THRESHOLD: 50000,
  },
  EUR: {
    TYPICAL_MIN: 45,
    TYPICAL_MAX: 22000,
    WARNING_THRESHOLD: 45000,
  },
  GBP: {
    TYPICAL_MIN: 40,
    TYPICAL_MAX: 20000,
    WARNING_THRESHOLD: 40000,
  },
} as const;

/**
 * Booking validation rules
 */
export const BOOKING_VALIDATION = {
  // Maximum number of nights for a single booking
  MAX_NIGHTS: 365,
  
  // Maximum number of guests
  MAX_ADULTS: 20,
  MAX_CHILDREN_5_13: 20,
  MAX_CHILDREN_0_4: 20,
  MAX_TOTAL_GUESTS: 50,
  
  // Maximum number of activities per booking
  MAX_ACTIVITIES: 50,
  
  // Date validation
  MIN_ADVANCE_BOOKING_DAYS: 1,
  MAX_ADVANCE_BOOKING_DAYS: 730, // 2 years
} as const;

/**
 * Error messages for validation failures
 */
export const VALIDATION_MESSAGES = {
  AMOUNT_TOO_LARGE: (amount: number, limit: number) => 
    `The booking amount (${amount.toLocaleString()}) exceeds our system limit of ${limit.toLocaleString()}. Please contact us directly for assistance with this booking.`,
  
  AMOUNT_NEGATIVE: (fieldName: string) => 
    `${fieldName} cannot be negative.`,
  
  BOOKING_TOO_LARGE: 
    "This booking is too large for our online system. Please contact us directly to complete your reservation.",
  
  DATABASE_OVERFLOW: 
    "The booking amount exceeds system limits. Please contact us directly to complete this booking.",
  
  INVALID_CURRENCY: (currency: string) => 
    `Invalid currency: ${currency}. Supported currencies are TZS, USD, EUR, and GBP.`,
} as const;

/**
 * Contact information for large bookings
 */
export const CONTACT_INFO = {
  EMAIL: "<EMAIL>",
  PHONE: "+255 123 456 789",
  WHATSAPP: "+255 123 456 789",
} as const;

/**
 * Utility functions for validation
 */
export const ValidationUtils = {
  /**
   * Check if a booking amount is within database limits
   */
  isAmountValid: (amount: number): boolean => {
    return amount >= DATABASE_LIMITS.BOOKING_AMOUNT_MIN && 
           amount <= DATABASE_LIMITS.BOOKING_AMOUNT_MAX;
  },

  /**
   * Check if a booking amount is approaching the warning threshold
   */
  isAmountNearLimit: (amount: number): boolean => {
    return amount > DATABASE_LIMITS.BOOKING_AMOUNT_WARNING_THRESHOLD;
  },

  /**
   * Round amount to match database precision
   */
  roundToDatabasePrecision: (amount: number): number => {
    return Math.round(amount * 100) / 100;
  },

  /**
   * Get appropriate error message for amount validation
   */
  getAmountErrorMessage: (amount: number): string => {
    if (amount < 0) {
      return VALIDATION_MESSAGES.AMOUNT_NEGATIVE("Amount");
    }
    if (amount > DATABASE_LIMITS.BOOKING_AMOUNT_MAX) {
      return VALIDATION_MESSAGES.AMOUNT_TOO_LARGE(amount, DATABASE_LIMITS.BOOKING_AMOUNT_MAX);
    }
    return "";
  },
} as const;
