import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>eader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import {
  Building,
  Calendar,
  Users,
  Settings,
  RefreshCw,
  AlertCircle,
} from "lucide-react";
import {
  useDashboardData,
  getDashboardMetrics,
  getSystemStatus,
} from "@/hooks/use-dashboard-data";

export default function Dashboard() {
  const { stats, recentActivities, loading, error, refreshData } =
    useDashboardData();
  const metrics = getDashboardMetrics(stats);
  const systemStatus = getSystemStatus(stats);

  const getActivityIcon = (type: string) => {
    switch (type) {
      case "booking":
        return "bg-green-500";
      case "accommodation":
        return "bg-blue-500";
      case "activity":
        return "bg-amber-500";
      default:
        return "bg-gray-500";
    }
  };

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInMinutes = Math.floor(
      (now.getTime() - time.getTime()) / (1000 * 60)
    );

    if (diffInMinutes < 1) return "Just now";
    if (diffInMinutes < 60) return `${diffInMinutes} minutes ago`;

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours} hours ago`;

    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays} days ago`;
  };

  const dashboardStats = [
    {
      title: "Total Accommodations",
      value: loading ? "..." : metrics.totalAccommodations.toString(),
      description: stats
        ? `${stats.accommodations.published} published`
        : "Loading...",
      icon: Building,
      color: "text-blue-600",
    },
    {
      title: "Total Activities",
      value: loading ? "..." : metrics.totalActivities.toString(),
      description: stats
        ? `${stats.activities.published_activities} published`
        : "Loading...",
      icon: Calendar,
      color: "text-green-600",
    },
    {
      title: "Total Bookings",
      value: loading ? "..." : metrics.totalBookings.toString(),
      description: stats
        ? `$${metrics.totalRevenue.toLocaleString()} revenue`
        : "Loading...",
      icon: Users,
      color: "text-amber-600",
    },
    {
      title: "System Status",
      value: systemStatus.status,
      description: systemStatus.description,
      icon: Settings,
      color: systemStatus.color,
    },
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-2">
            Welcome to Malombo Selous Forest Camp Admin Panel
          </p>
        </div>
        <Button
          onClick={refreshData}
          disabled={loading}
          variant="outline"
          size="sm"
          className="flex items-center gap-2"
        >
          <RefreshCw className={`h-4 w-4 ${loading ? "animate-spin" : ""}`} />
          Refresh
        </Button>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {error}
            <Button
              onClick={refreshData}
              variant="outline"
              size="sm"
              className="ml-2"
            >
              Try Again
            </Button>
          </AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {dashboardStats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Card key={index} className="hover:shadow-lg transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600">
                  {stat.title}
                </CardTitle>
                <Icon className={`h-4 w-4 ${stat.color}`} />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-gray-900">
                  {stat.value}
                </div>
                <p className="text-xs text-gray-500 mt-1">{stat.description}</p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>Latest updates and changes</CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <div
                    key={i}
                    className="flex items-center space-x-4 animate-pulse"
                  >
                    <div className="w-2 h-2 bg-gray-200 rounded-full"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-1"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                ))}
              </div>
            ) : recentActivities.length > 0 ? (
              <div className="space-y-4">
                {recentActivities.map((activity) => (
                  <div
                    key={activity.id}
                    className="flex items-center space-x-4"
                  >
                    <div
                      className={`w-2 h-2 rounded-full ${getActivityIcon(
                        activity.type
                      )}`}
                    ></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">{activity.action}</p>
                      <p className="text-xs text-gray-500">
                        {activity.description}
                      </p>
                      <p className="text-xs text-gray-400">
                        {formatTimeAgo(activity.timestamp)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500">No recent activity</p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>System Overview</CardTitle>
            <CardDescription>Current system statistics</CardDescription>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="space-y-4">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-2/3 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/3"></div>
                </div>
              </div>
            ) : stats ? (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">
                    Published Accommodations
                  </span>
                  <span className="text-sm text-gray-500">
                    {stats.accommodations.published}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">
                    Draft Accommodations
                  </span>
                  <span className="text-sm text-gray-500">
                    {stats.accommodations.draft}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">
                    Featured Accommodations
                  </span>
                  <span className="text-sm text-gray-500">
                    {stats.accommodations.featured}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">
                    Published Activities
                  </span>
                  <span className="text-sm text-gray-500">
                    {stats.activities.published_activities}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">
                    Confirmed Bookings
                  </span>
                  <span className="text-sm text-gray-500">
                    {stats.bookings.confirmed_bookings}
                  </span>
                </div>
              </div>
            ) : (
              <p className="text-sm text-gray-500">No data available</p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
            <CardDescription>Common administrative tasks</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Button
                variant="outline"
                className="w-full justify-start h-auto p-3"
                onClick={() =>
                  (window.location.href = "/admin/accommodations/new")
                }
              >
                <div className="text-left">
                  <div className="font-medium">Add New Accommodation</div>
                  <div className="text-sm text-gray-500">
                    Create a new room or suite
                  </div>
                </div>
              </Button>
              <Button
                variant="outline"
                className="w-full justify-start h-auto p-3"
                onClick={() => (window.location.href = "/admin/activities/new")}
              >
                <div className="text-left">
                  <div className="font-medium">Add New Activity</div>
                  <div className="text-sm text-gray-500">
                    Create a new safari experience
                  </div>
                </div>
              </Button>
              <Button
                variant="outline"
                className="w-full justify-start h-auto p-3"
                onClick={() => (window.location.href = "/admin/bookings")}
              >
                <div className="text-left">
                  <div className="font-medium">View Bookings</div>
                  <div className="text-sm text-gray-500">
                    Manage reservations
                  </div>
                </div>
              </Button>
              <Button
                variant="outline"
                className="w-full justify-start h-auto p-3"
                onClick={() => (window.location.href = "/admin/bookings/new")}
              >
                <div className="text-left">
                  <div className="font-medium">Create Booking</div>
                  <div className="text-sm text-gray-500">
                    Add a new reservation
                  </div>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
