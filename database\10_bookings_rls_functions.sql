-- =====================================================
-- Malombo Admin Panel - Bookings RLS Functions
-- =====================================================
-- Additional RLS policies and helper functions for bookings

-- Function to check if user can access booking data
DROP FUNCTION IF EXISTS can_access_bookings();

CREATE OR REPLACE FUNCTION can_access_bookings()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE profiles.id = auth.uid() 
        AND profiles.role IN ('admin', 'staff')
        AND profiles.is_active = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is admin
DROP FUNCTION IF EXISTS is_admin();
DROP FUNCTION IF EXISTS is_admin(UUID);

CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM public.profiles 
        WHERE profiles.id = auth.uid() 
        AND profiles.role = 'admin'
        AND profiles.is_active = true
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get bookings with filters and pagination
-- Drop all possible versions of the function
DROP FUNCTION IF EXISTS get_bookings_filtered(TEXT, TEXT, TEXT, UUID, TEXT, DATE, DATE, TEXT, TEXT, INTEGER, INTEGER);
DROP FUNCTION IF EXISTS get_bookings_filtered(text,character varying,character varying,uuid,character varying,date,date,character varying,character varying,integer,integer);
DROP FUNCTION IF EXISTS get_bookings_filtered(TEXT, VARCHAR, VARCHAR, UUID, VARCHAR, DATE, DATE, VARCHAR, VARCHAR, INTEGER, INTEGER);

CREATE OR REPLACE FUNCTION get_bookings_filtered(
    search_term TEXT DEFAULT NULL,
    status_filter TEXT DEFAULT NULL,
    payment_filter TEXT DEFAULT NULL,
    accommodation_filter UUID DEFAULT NULL,
    source_filter TEXT DEFAULT NULL,
    date_from DATE DEFAULT NULL,
    date_to DATE DEFAULT NULL,
    sort_by TEXT DEFAULT 'check_in',
    sort_order TEXT DEFAULT 'desc',
    page_limit INTEGER DEFAULT 10,
    page_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    guest_full_name VARCHAR(255),
    email VARCHAR(255),
    phone VARCHAR(50),
    country VARCHAR(100),
    adults INTEGER,
    children_5_13 INTEGER,
    children_0_4 INTEGER,
    accommodation_name TEXT,
    accommodation_type TEXT,
    check_in DATE,
    check_out DATE,
    nights INTEGER,
    currency VARCHAR(3),
    total_amount DECIMAL(10,2),
    status VARCHAR(50),
    payment_status VARCHAR(50),
    source VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE,
    total_count BIGINT
) AS $$
DECLARE
    query_text TEXT;
    count_query TEXT;
    total_records BIGINT;
BEGIN
    -- Check permissions
    IF NOT can_access_bookings() THEN
        RAISE EXCEPTION 'Access denied';
    END IF;

    -- Build the base query
    query_text := '
        SELECT 
            b.id,
            b.guest_full_name,
            b.email,
            b.phone,
            b.country,
            b.adults,
            b.children_5_13,
            b.children_0_4,
            a.name as accommodation_name,
            a.type as accommodation_type,
            b.check_in,
            b.check_out,
            b.nights,
            b.currency,
            b.total_amount,
            b.status,
            b.payment_status,
            b.source,
            b.created_at
        FROM public.bookings b
        LEFT JOIN public.accommodations a ON a.id = b.accommodation_id
        WHERE 1=1';

    -- Add filters
    IF search_term IS NOT NULL AND search_term != '' THEN
        query_text := query_text || ' AND (
            b.guest_full_name ILIKE ''%' || search_term || '%'' OR
            b.email ILIKE ''%' || search_term || '%'' OR
            b.phone ILIKE ''%' || search_term || '%'' OR
            a.name ILIKE ''%' || search_term || '%''
        )';
    END IF;

    IF status_filter IS NOT NULL AND status_filter != 'all' THEN
        query_text := query_text || ' AND b.status = ''' || status_filter || '''';
    END IF;

    IF payment_filter IS NOT NULL AND payment_filter != 'all' THEN
        query_text := query_text || ' AND b.payment_status = ''' || payment_filter || '''';
    END IF;

    IF accommodation_filter IS NOT NULL THEN
        query_text := query_text || ' AND b.accommodation_id = ''' || accommodation_filter || '''';
    END IF;

    IF source_filter IS NOT NULL AND source_filter != 'all' THEN
        query_text := query_text || ' AND b.source = ''' || source_filter || '''';
    END IF;

    IF date_from IS NOT NULL THEN
        query_text := query_text || ' AND b.check_in >= ''' || date_from || '''';
    END IF;

    IF date_to IS NOT NULL THEN
        query_text := query_text || ' AND b.check_in <= ''' || date_to || '''';
    END IF;

    -- Get total count
    count_query := 'SELECT COUNT(*) FROM (' || query_text || ') as count_query';
    EXECUTE count_query INTO total_records;

    -- Add sorting
    IF sort_by = 'guest_name' THEN
        query_text := query_text || ' ORDER BY b.guest_full_name';
    ELSIF sort_by = 'check_in' THEN
        query_text := query_text || ' ORDER BY b.check_in';
    ELSIF sort_by = 'created_at' THEN
        query_text := query_text || ' ORDER BY b.created_at';
    ELSIF sort_by = 'total_amount' THEN
        query_text := query_text || ' ORDER BY b.total_amount';
    ELSE
        query_text := query_text || ' ORDER BY b.check_in';
    END IF;

    IF sort_order = 'asc' THEN
        query_text := query_text || ' ASC';
    ELSE
        query_text := query_text || ' DESC';
    END IF;

    -- Add pagination
    query_text := query_text || ' LIMIT ' || page_limit || ' OFFSET ' || page_offset;

    -- Execute and return results with total count
    RETURN QUERY EXECUTE '
        SELECT *, ' || total_records || '::BIGINT as total_count 
        FROM (' || query_text || ') as paginated_results';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update booking status with history tracking
DROP FUNCTION IF EXISTS update_booking_status(UUID, TEXT, TEXT);
DROP FUNCTION IF EXISTS update_booking_status(UUID, VARCHAR(50), TEXT);
DROP FUNCTION IF EXISTS update_booking_status(UUID, character varying, text);

CREATE OR REPLACE FUNCTION update_booking_status(
    booking_id UUID,
    new_status TEXT,
    admin_notes TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    current_status TEXT;
    user_id UUID;
BEGIN
    -- Check permissions
    IF NOT can_access_bookings() THEN
        RAISE EXCEPTION 'Access denied';
    END IF;

    -- Validate status
    IF new_status NOT IN ('new', 'confirmed', 'cancelled', 'completed') THEN
        RAISE EXCEPTION 'Invalid status: %', new_status;
    END IF;

    -- Get current user
    user_id := auth.uid();

    -- Get current status
    SELECT status INTO current_status 
    FROM public.bookings 
    WHERE id = booking_id;

    IF current_status IS NULL THEN
        RAISE EXCEPTION 'Booking not found';
    END IF;

    -- Update the booking
    UPDATE public.bookings 
    SET 
        status = new_status,
        updated_by = user_id,
        notes_admin = CASE 
            WHEN admin_notes IS NOT NULL THEN 
                COALESCE(notes_admin, '') || E'\n[' || NOW()::TEXT || '] Status changed to ' || new_status || ': ' || admin_notes
            ELSE notes_admin
        END
    WHERE id = booking_id;

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update payment status
DROP FUNCTION IF EXISTS update_payment_status(UUID, TEXT, TEXT, TEXT);
DROP FUNCTION IF EXISTS update_payment_status(UUID, VARCHAR(50), VARCHAR(100), VARCHAR(255));
DROP FUNCTION IF EXISTS update_payment_status(UUID, character varying, character varying, character varying);

CREATE OR REPLACE FUNCTION update_payment_status(
    booking_id UUID,
    new_payment_status TEXT,
    payment_method_val TEXT DEFAULT NULL,
    payment_ref TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    user_id UUID;
BEGIN
    -- Check permissions
    IF NOT can_access_bookings() THEN
        RAISE EXCEPTION 'Access denied';
    END IF;

    -- Validate payment status
    IF new_payment_status NOT IN ('unpaid', 'deposit_paid', 'paid', 'refunded') THEN
        RAISE EXCEPTION 'Invalid payment status: %', new_payment_status;
    END IF;

    -- Get current user
    user_id := auth.uid();

    -- Update the booking
    UPDATE public.bookings 
    SET 
        payment_status = new_payment_status,
        payment_method = COALESCE(payment_method_val, payment_method),
        payment_reference = COALESCE(payment_ref, payment_reference),
        updated_by = user_id
    WHERE id = booking_id;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Booking not found';
    END IF;

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get booking statistics
DROP FUNCTION IF EXISTS get_booking_statistics(DATE, DATE);
DROP FUNCTION IF EXISTS get_booking_statistics(date, date);

CREATE OR REPLACE FUNCTION get_booking_statistics(
    date_from DATE DEFAULT NULL,
    date_to DATE DEFAULT NULL
)
RETURNS TABLE (
    total_bookings BIGINT,
    confirmed_bookings BIGINT,
    pending_bookings BIGINT,
    cancelled_bookings BIGINT,
    completed_bookings BIGINT,
    total_revenue DECIMAL(10,2),
    paid_revenue DECIMAL(10,2),
    pending_revenue DECIMAL(10,2),
    average_booking_value DECIMAL(10,2),
    total_guests INTEGER
) AS $$
BEGIN
    -- Check permissions
    IF NOT can_access_bookings() THEN
        RAISE EXCEPTION 'Access denied';
    END IF;

    RETURN QUERY
    SELECT 
        COUNT(*)::BIGINT as total_bookings,
        COUNT(CASE WHEN status = 'confirmed' THEN 1 END)::BIGINT as confirmed_bookings,
        COUNT(CASE WHEN status = 'new' THEN 1 END)::BIGINT as pending_bookings,
        COUNT(CASE WHEN status = 'cancelled' THEN 1 END)::BIGINT as cancelled_bookings,
        COUNT(CASE WHEN status = 'completed' THEN 1 END)::BIGINT as completed_bookings,
        COALESCE(SUM(total_amount), 0) as total_revenue,
        COALESCE(SUM(CASE WHEN payment_status IN ('paid', 'deposit_paid') THEN total_amount ELSE 0 END), 0) as paid_revenue,
        COALESCE(SUM(CASE WHEN payment_status = 'unpaid' THEN total_amount ELSE 0 END), 0) as pending_revenue,
        COALESCE(AVG(total_amount), 0) as average_booking_value,
        COALESCE(SUM(adults + children_5_13 + children_0_4), 0)::INTEGER as total_guests
    FROM public.bookings
    WHERE 
        (date_from IS NULL OR check_in >= date_from) AND
        (date_to IS NULL OR check_in <= date_to) AND
        status != 'cancelled';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION can_access_bookings() TO authenticated;
GRANT EXECUTE ON FUNCTION is_admin() TO authenticated;
GRANT EXECUTE ON FUNCTION get_bookings_filtered(TEXT, TEXT, TEXT, UUID, TEXT, DATE, DATE, TEXT, TEXT, INTEGER, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION update_booking_status(UUID, TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION update_payment_status(UUID, TEXT, TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION get_booking_statistics(DATE, DATE) TO authenticated;

-- Create indexes for the helper functions
CREATE INDEX IF NOT EXISTS idx_bookings_search ON public.bookings USING GIN(
    to_tsvector('english', guest_full_name || ' ' || email || ' ' || phone)
);

COMMENT ON FUNCTION get_bookings_filtered IS 'Get filtered and paginated bookings with accommodation details';
COMMENT ON FUNCTION update_booking_status IS 'Update booking status with automatic history tracking';
COMMENT ON FUNCTION update_payment_status IS 'Update payment status and related fields';
COMMENT ON FUNCTION get_booking_statistics IS 'Get booking statistics for dashboard and reports';
