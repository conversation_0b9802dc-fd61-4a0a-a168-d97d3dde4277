# Malombo Accommodations Management System

## Overview

The Accommodations Management System is a comprehensive CRUD interface for managing accommodation listings in the Malombo Selous Forest Camp admin panel. This system provides full functionality for creating, editing, deleting, and managing accommodations with image uploads, status controls, and public site integration.

## ✅ Features Implemented

### Database & Storage
- **Accommodations Table**: Complete schema with all required fields
- **Supabase Storage**: Dedicated bucket for accommodation images
- **Row Level Security**: Proper access control for admin/staff users
- **Data Validation**: Server-side validation with triggers
- **Helper Functions**: Bulk operations and search functionality

### Admin Interface
- **List View**: Searchable, filterable, sortable table with pagination
- **Create/Edit Forms**: Comprehensive forms with validation
- **Image Management**: Multiple image upload with preview and removal
- **Status Controls**: Draft/Published/Unpublished with featured toggle
- **Responsive Design**: Mobile-optimized interface

### Public API Integration
- **Published Only**: Only published accommodations visible to public
- **Featured Priority**: Featured accommodations prioritized in listings
- **Search & Filter**: Public search and filtering capabilities
- **Data Transformation**: Clean data formatting for public consumption

## 📁 File Structure

```
src/
├── types/
│   └── accommodation.ts          # TypeScript interfaces and types
├── lib/
│   ├── accommodations.ts         # Admin CRUD operations
│   └── public-accommodations.ts  # Public API functions
├── pages/admin/
│   ├── AccommodationsAdmin.tsx   # Main list page
│   └── AccommodationForm.tsx     # Create/edit form
├── utils/
│   └── accommodation-validation.ts # Validation utilities
└── tests/
    └── accommodations.test.ts    # Integration tests

database/
├── 05_create_accommodations_table.sql    # Table creation
└── 06_accommodations_rls_functions.sql   # RLS policies & functions
```

## 🚀 Setup Instructions

### 1. Database Setup

Run the SQL scripts in order:

```sql
-- 1. Create accommodations table and storage bucket
\i database/05_create_accommodations_table.sql

-- 2. Set up RLS policies and helper functions
\i database/06_accommodations_rls_functions.sql
```

### 2. Environment Variables

Ensure your `.env` file includes:

```env
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 3. Dependencies

The following dependencies are required (already installed):

```json
{
  "react-hook-form": "^7.62.0",
  "sonner": "^2.0.7"
}
```

## 🎯 Usage Guide

### Admin Panel Access

1. **Navigate to Accommodations**: `/admin/accommodations`
2. **Create New**: Click "Add Accommodation" button
3. **Edit Existing**: Click edit icon in table row
4. **Delete**: Click delete icon with confirmation
5. **Toggle Status**: Use eye/eye-off icons for publish/unpublish
6. **Toggle Featured**: Use star icons for featured status

### Form Fields

#### Required Fields
- **Name**: Accommodation name (3-100 characters)
- **Type**: Select from predefined types (banda, room, tent, etc.)
- **Description**: Detailed description (10-2000 characters)
- **Price Range**: Format like "$180–$380" or "$180"
- **Capacity**: Number of guests (1-20)
- **Images**: At least 1 image required (max 10, 10MB each)

#### Optional Fields
- **Special Features**: Additional features description
- **Amenities**: Multi-select from predefined list
- **Status**: Draft/Published/Unpublished
- **Featured**: Checkbox for homepage highlighting

### Image Management

- **Upload**: Drag & drop or click to select multiple images
- **Preview**: Real-time preview of uploaded images
- **Remove**: Click X button to remove images
- **Formats**: JPEG, PNG, WebP supported
- **Size Limit**: 10MB per image

### Search & Filtering

- **Search**: By name or description
- **Type Filter**: Filter by accommodation type
- **Status Filter**: Filter by publication status
- **Featured Filter**: Show only featured accommodations
- **Sorting**: Click column headers to sort
- **Pagination**: 10 items per page with navigation

## 🔧 API Reference

### Admin Operations

```typescript
// Create accommodation
const result = await createAccommodation(accommodationData);

// Update accommodation
const result = await updateAccommodation({ id, ...updateData });

// Delete accommodation
const result = await deleteAccommodation(id);

// Get accommodations with filters
const result = await getAccommodations(filters, sort, pagination);

// Upload image
const result = await uploadAccommodationImage(file, accommodationId);

// Update status
const result = await updateAccommodationStatus(id, 'published');

// Toggle featured
const result = await toggleAccommodationFeatured(id, true);
```

### Public API

```typescript
// Get published accommodations
const accommodations = await getPublishedAccommodations();

// Get featured accommodations
const featured = await getFeaturedAccommodations(6);

// Get by type
const suites = await getAccommodationsByType('suite');

// Search
const results = await searchPublishedAccommodations('safari');
```

## 🧪 Testing

### Automated Tests

Run the test suite:

```bash
npm run test src/tests/accommodations.test.ts
```

### Manual Testing

Use browser console utilities:

```javascript
// Create test data
await window.testAccommodations.createTestData();

// Test CRUD operations
await window.testAccommodations.testCRUD();

// Test public API
await window.testAccommodations.testPublicAPI();

// Cleanup test data
await window.testAccommodations.cleanup();
```

## 🔒 Security Features

### Row Level Security
- **Admin Access**: Full CRUD operations
- **Staff Access**: Read-only access
- **Public Access**: Published accommodations only

### Data Validation
- **Client-side**: Real-time form validation
- **Server-side**: Database triggers and constraints
- **File Upload**: Type and size validation
- **SQL Injection**: Parameterized queries

### Access Control
- **Authentication**: Required for admin operations
- **Authorization**: Role-based permissions
- **Image Storage**: Secure Supabase storage with policies

## 🎨 Styling & Theming

### Safari-Inspired Theme
- **Colors**: Earthy browns, greens, golden highlights
- **Gradients**: Subtle background gradients
- **Typography**: Consistent font hierarchy
- **Icons**: Lucide React icons throughout

### Responsive Design
- **Mobile-First**: Optimized for mobile devices
- **Tablet Support**: Adapted layouts for tablets
- **Desktop**: Full-featured desktop interface
- **Touch-Friendly**: Large touch targets

## 🚀 Deployment Notes

### Production Checklist
- [ ] Database scripts executed
- [ ] Environment variables configured
- [ ] Storage bucket created and configured
- [ ] RLS policies enabled
- [ ] Admin users created
- [ ] Image upload tested
- [ ] Public API tested

### Performance Considerations
- **Pagination**: Implemented for large datasets
- **Image Optimization**: Recommended for production
- **Caching**: Consider implementing for public API
- **Indexing**: Database indexes for search performance

## 🔄 Integration with Public Site

### Data Flow
1. **Admin Creates**: Accommodation in draft status
2. **Admin Publishes**: Status changed to published
3. **Public Queries**: Only published accommodations returned
4. **Featured Priority**: Featured accommodations shown first

### Public Site Usage
```typescript
// Homepage featured accommodations
const featured = await getFeaturedAccommodations(6);

// Accommodations page
const all = await getPublishedAccommodations();

// Search functionality
const results = await searchPublishedAccommodations(query);
```

## 📞 Support & Maintenance

### Common Issues
- **Image Upload Fails**: Check storage bucket permissions
- **Validation Errors**: Review form field requirements
- **Access Denied**: Verify user roles and RLS policies
- **Search Not Working**: Check database indexes

### Monitoring
- **Error Logging**: Check browser console and Supabase logs
- **Performance**: Monitor query performance in Supabase
- **Storage Usage**: Monitor image storage usage

---

**Status**: ✅ Complete and Ready for Production

**Last Updated**: 2025-08-28

**Version**: 1.0.0
