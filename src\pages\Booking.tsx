import { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { usePageTitle } from "@/hooks/use-page-title";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { StepProgress } from "@/components/booking/BookingStep";
import { AccommodationCard } from "@/components/booking/AccommodationCard";
import { ActivitySelector } from "@/components/booking/ActivitySelector";
import { PriceCalculator } from "@/components/booking/PriceCalculator";
import { CountrySelect } from "@/components/ui/country-select";
import {
  getPublishedAccommodations,
  getPublishedAccommodation,
} from "@/lib/public-accommodations";
import { getPublishedActivities } from "@/lib/activities";
import type {
  Accommodation,
  Activity,
  PricingBreakdown,
} from "@/types/booking";
import type { Accommodation as DBAccommodation } from "@/types/accommodation";
import type { Activity as DBActivity } from "@/types/activity";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

import {
  CalendarIcon,
  Minus,
  Plus,
  CheckCircle,
  Mail,
  Phone,
  MapPin,
  Users,
} from "lucide-react";
import { format, differenceInDays } from "date-fns";

// Form validation schema
const guestInfoSchema = z.object({
  firstName: z.string().min(2, "First name must be at least 2 characters"),
  lastName: z.string().min(2, "Last name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  phone: z.string().min(10, "Please enter a valid phone number"),
  country: z.string().min(2, "Please select your country"),
  specialRequests: z.string().optional(),
  dietaryRequirements: z.string().optional(),
  emergencyContact: z.object({
    name: z.string().min(2, "Emergency contact name is required"),
    phone: z.string().min(10, "Emergency contact phone is required"),
    relationship: z.string().min(2, "Relationship is required"),
  }),
  termsAccepted: z
    .boolean()
    .refine((val) => val === true, "You must accept the terms and conditions"),
});

type GuestInfoFormData = z.infer<typeof guestInfoSchema>;

const Booking = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [currentStep, setCurrentStep] = useState(1);
  usePageTitle("Booking");

  // Data loading state
  const [accommodations, setAccommodations] = useState<Accommodation[]>([]);
  const [activities, setActivities] = useState<Activity[]>([]);
  const [loadingAccommodations, setLoadingAccommodations] = useState(true);
  const [loadingActivities, setLoadingActivities] = useState(true);
  const [dataError, setDataError] = useState<string | null>(null);

  // Booking state
  const [selectedAccommodation, setSelectedAccommodation] =
    useState<Accommodation | null>(null);
  const [checkIn, setCheckIn] = useState<Date | undefined>(undefined);
  const [checkOut, setCheckOut] = useState<Date | undefined>(undefined);
  const [adults, setAdults] = useState(2);
  const [children, setChildren] = useState(0);
  const [selectedActivities, setSelectedActivities] = useState<Activity[]>([]);
  const [bookingReference, setBookingReference] = useState<string>("");
  const [isBookingComplete, setIsBookingComplete] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Form for guest information
  const form = useForm<GuestInfoFormData>({
    resolver: zodResolver(guestInfoSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      country: "",
      specialRequests: "",
      dietaryRequirements: "",
      emergencyContact: {
        name: "",
        phone: "",
        relationship: "",
      },
      termsAccepted: false,
    },
  });

  // Convert database accommodation to booking accommodation
  const convertAccommodation = (
    dbAccommodation: DBAccommodation
  ): Accommodation => ({
    id: dbAccommodation.id,
    title: dbAccommodation.name,
    description: dbAccommodation.description || "",
    detailedDescription: dbAccommodation.description || "",
    image:
      dbAccommodation.images?.[0] || "/images/placeholder-accommodation.jpg",
    pricePerNight: parseFloat(
      dbAccommodation.price_range?.replace(/[^0-9.-]+/g, "") || "0"
    ),
    maxGuests: dbAccommodation.capacity,
    category: dbAccommodation.type as "luxury" | "premium" | "standard",
    size: dbAccommodation.special_features || "Standard size",
    bedConfiguration: `${dbAccommodation.type} accommodation`,
    amenities: dbAccommodation.amenities || [],
    features: dbAccommodation.special_features
      ? [dbAccommodation.special_features]
      : [],
  });

  // Convert database activity to booking activity
  const convertActivity = (dbActivity: DBActivity): Activity => ({
    id: dbActivity.id,
    name: dbActivity.title,
    description: dbActivity.description || "",
    price: parseFloat(dbActivity.pricing?.replace(/[^0-9.-]+/g, "") || "0"),
    duration: dbActivity.duration || "Half day",
    included: false,
    category: dbActivity.category,
    maxParticipants: 8, // Default value
    ageRestriction: "All ages welcome", // Default value
  });

  // Load accommodations and activities data
  useEffect(() => {
    const loadData = async () => {
      try {
        // Load accommodations
        setLoadingAccommodations(true);
        const dbAccommodations = await getPublishedAccommodations();
        const convertedAccommodations =
          dbAccommodations.map(convertAccommodation);
        setAccommodations(convertedAccommodations);

        // Load activities
        setLoadingActivities(true);
        const dbActivities = await getPublishedActivities();
        const convertedActivities = dbActivities.map(convertActivity);
        setActivities(convertedActivities);

        // Handle pre-selected accommodation from URL
        const accommodationId = searchParams.get("accommodation");
        if (accommodationId) {
          const selectedAcc = convertedAccommodations.find(
            (acc) => acc.id === accommodationId
          );
          if (selectedAcc) {
            setSelectedAccommodation(selectedAcc);
          }
        }
      } catch (error) {
        console.error("Error loading booking data:", error);
        setDataError("Failed to load booking data. Please refresh the page.");
      } finally {
        setLoadingAccommodations(false);
        setLoadingActivities(false);
      }
    };

    loadData();
  }, [searchParams]);

  const steps = [
    {
      title: "Accommodation & Dates",
      description: "Choose your stay details",
      sn: 0,
    },
    {
      title: "Activities",
      description: "Select your safari experiences",
      sn: 1,
    },
    { title: "Guest Information", description: "Provide your details", sn: 2 },
    { title: "Confirmation", description: "Review and confirm booking", sn: 3 },
  ];

  const calculatePricing = (): PricingBreakdown => {
    if (!selectedAccommodation || !checkIn || !checkOut) {
      return {
        accommodationPrice: 0,
        nights: 0,
        accommodationTotal: 0,
        activitiesTotal: 0,
        subtotal: 0,
        taxes: 0,
        total: 0,
      };
    }

    const nights = differenceInDays(checkOut, checkIn);
    const guestCount = adults + children;
    const accommodationTotal = selectedAccommodation.pricePerNight * nights;
    const activitiesTotal = selectedActivities.reduce(
      (total, activity) => total + activity.price * guestCount,
      0
    );
    const subtotal = accommodationTotal + activitiesTotal;

    // Apply discount for 3+ nights
    const discount = nights >= 3 ? subtotal * 0.1 : 0;
    const discountedSubtotal = subtotal - discount;

    const taxes = discountedSubtotal * 0.18; // 18% VAT
    const total = discountedSubtotal + taxes;

    // Log warning if amounts are getting close to database limits
    const MAX_VALUE = 99999999.99;
    const WARNING_THRESHOLD = MAX_VALUE * 0.8; // 80% of max value

    if (total > WARNING_THRESHOLD) {
      console.warn(
        `Booking total ${total} is approaching database limit ${MAX_VALUE}`
      );
    }

    return {
      accommodationPrice: selectedAccommodation.pricePerNight,
      nights,
      accommodationTotal,
      activitiesTotal,
      subtotal: discountedSubtotal,
      taxes,
      total,
    };
  };

  const pricing = calculatePricing();
  const guestCount = adults + children;

  const handleActivityToggle = (activity: Activity) => {
    setSelectedActivities((prev) => {
      const isSelected = prev.some((a) => a.id === activity.id);
      if (isSelected) {
        return prev.filter((a) => a.id !== activity.id);
      } else {
        return [...prev, activity];
      }
    });
  };

  const canProceedToStep2 =
    selectedAccommodation && checkIn && checkOut && checkIn < checkOut;
  const canProceedToStep3 = canProceedToStep2;
  const canProceedToStep4 = canProceedToStep3 && form.formState.isValid;

  const generateBookingReference = () => {
    const prefix = "MLB";
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.random().toString(36).substring(2, 5).toUpperCase();
    return `${prefix}${timestamp}${random}`;
  };

  const validateNumericField = (value: number, fieldName: string): number => {
    // Import constants dynamically to avoid circular dependencies
    const MAX_VALUE = 99999999.99; // DATABASE_LIMITS.BOOKING_AMOUNT_MAX

    if (value > MAX_VALUE) {
      console.error(
        `${fieldName} value ${value} exceeds database limit ${MAX_VALUE}`
      );
      throw new Error(
        `The ${fieldName.toLowerCase()} amount is too large. Please contact us directly for assistance with this booking.`
      );
    }

    if (value < 0) {
      console.error(`${fieldName} value ${value} cannot be negative`);
      throw new Error(`Invalid ${fieldName.toLowerCase()} amount.`);
    }

    // Round to 2 decimal places to match database precision
    return Math.round(value * 100) / 100;
  };

  const handleCompleteBooking = async (formData: GuestInfoFormData) => {
    try {
      setIsSubmitting(true);

      // Generate booking reference
      const reference = generateBookingReference();

      // Validate all numeric fields before sending to database
      const validatedBaseAmount = validateNumericField(
        pricing.subtotal,
        "Base amount"
      );
      const validatedTaxes = validateNumericField(pricing.taxes, "Taxes");
      const validatedTotal = validateNumericField(
        pricing.total,
        "Total amount"
      );

      console.log("Booking validation:", {
        base_amount: validatedBaseAmount,
        taxes: validatedTaxes,
        total_amount: validatedTotal,
        currency: "TZS",
      });

      // Prepare booking data
      const bookingData = {
        guest_full_name: `${formData.firstName} ${formData.lastName}`,
        email: formData.email,
        phone: formData.phone,
        country: formData.country,
        adults: adults,
        children_5_13: children, // For now, treat all children as 5-13 age group
        children_0_4: 0, // Set to 0 for now, can be enhanced later with age breakdown
        accommodation_id: selectedAccommodation?.id || "",
        check_in: checkIn?.toISOString().split("T")[0] || "",
        check_out: checkOut?.toISOString().split("T")[0] || "",
        activity_ids: selectedActivities.map((activity) => activity.id),
        currency: "TZS" as const,
        base_amount: validatedBaseAmount,
        discounts: 0,
        taxes: validatedTaxes,
        total_amount: validatedTotal,
        status: "new" as const,
        payment_status: "unpaid" as const,
        source: "website" as const,
        notes_guest: [
          formData.specialRequests &&
            `Special Requests: ${formData.specialRequests}`,
          formData.dietaryRequirements &&
            `Dietary Requirements: ${formData.dietaryRequirements}`,
          `Emergency Contact: ${formData.emergencyContact.name} (${formData.emergencyContact.relationship}) - ${formData.emergencyContact.phone}`,
        ]
          .filter(Boolean)
          .join("\n"),
      };

      // Import createBooking function
      const { createBooking } = await import("@/lib/bookings");

      // Create the booking
      const response = await createBooking(bookingData);

      if (response.success) {
        setBookingReference(reference);
        setIsBookingComplete(true);
        setCurrentStep(4);

        // Optional: Send confirmation email to admin (not required for user)
        try {
          const { sendBookingConfirmation } = await import("@/lib/email");
          await sendBookingConfirmation(response.data);
        } catch (emailError) {
          console.warn(
            "Email notification failed, but booking was successful:",
            emailError
          );
        }
      } else {
        throw new Error(response.message || "Failed to create booking");
      }
    } catch (error) {
      console.error("Error completing booking:", error);

      // Provide specific error messages based on error type
      let errorMessage =
        "There was an error completing your booking. Please try again or contact us directly.";

      if (error instanceof Error) {
        if (
          error.message.includes("too large") ||
          error.message.includes("exceeds database limit")
        ) {
          errorMessage = error.message;
        } else if (
          error.message.includes("22003") ||
          error.message.includes("numeric field overflow")
        ) {
          errorMessage =
            "The booking amount is too large for our system. Please contact us directly to complete this booking.";
        } else if (
          error.message.includes("Invalid") &&
          error.message.includes("amount")
        ) {
          errorMessage = error.message;
        }
      }

      alert(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleNextStep = () => {
    if (currentStep === 3) {
      // Validate form before proceeding to step 4
      form.handleSubmit(handleCompleteBooking)();
    } else if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  return (
    <div className="flex flex-col w-full min-h-screen">
      {/* Hero Section */}
      <section className="relative w-full h-[40vh] bg-gradient-to-r from-[#2C5530] to-[#8B4513]">
        <div className="absolute inset-0">
          <img
            src="/images/booking-hero.jpg"
            alt="Book Your Safari"
            className="w-full h-full object-cover opacity-30"
          />
        </div>
        <div className="relative z-10 container mx-auto h-full flex flex-col justify-center px-4">
          {/* Breadcrumb */}
          {/* <div className="mb-8">
            <Breadcrumb>
              <BreadcrumbList className="text-white/80">
                <BreadcrumbItem>
                  <BreadcrumbLink
                    onClick={() => navigate("/")}
                    className="text-white/80 hover:text-white cursor-pointer"
                  >
                    Home
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="text-white/60" />
                <BreadcrumbItem>
                  <BreadcrumbLink
                    onClick={() => navigate("/accommodations")}
                    className="text-white/80 hover:text-white cursor-pointer"
                  >
                    Accommodations
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="text-white/60" />
                <BreadcrumbItem>
                  <BreadcrumbPage className="text-white font-semibold">
                    Booking
                  </BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div> */}

          <h1 className="text-4xl md:text-5xl mb-4 playfair-display-heading text-white">
            Book Your <span className="text-[#DAA520]">Safari Adventure</span>
          </h1>
          <p className="text-lg md:text-xl mb-8 max-w-2xl playfair-display-subheading text-white/90">
            Complete your booking in just a few simple steps
          </p>
        </div>
      </section>

      {/* Booking Content */}
      <section className="py-16 w-[95%] mx-auto">
        <div className="container mx-auto px-4">
          {/* Step Progress */}
          <StepProgress
            currentStep={currentStep}
            totalSteps={4}
            steps={steps}
          />

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2">
              {/* Step 1: Accommodation & Dates */}
              {currentStep === 1 && (
                <Card className="border-t-4 border-t-[#2C5530]">
                  <CardContent className="p-6">
                    <h3 className="text-2xl font-bold playfair-display-heading text-[#2C5530] mb-6">
                      Choose Your Accommodation & Dates
                    </h3>

                    {/* Accommodation Selection */}
                    <div className="mb-8">
                      <h4 className="text-lg font-semibold text-[#2C5530] mb-4">
                        {selectedAccommodation &&
                        searchParams.get("accommodation")
                          ? "Selected Accommodation"
                          : "Select Accommodation"}
                      </h4>

                      {loadingAccommodations ? (
                        <div className="flex justify-center items-center py-8">
                          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#2C5530]"></div>
                          <span className="ml-2 text-gray-600">
                            Loading accommodations...
                          </span>
                        </div>
                      ) : dataError ? (
                        <div className="text-center py-8">
                          <p className="text-red-600 mb-4">{dataError}</p>
                          <Button
                            onClick={() => window.location.reload()}
                            variant="outline"
                          >
                            Retry
                          </Button>
                        </div>
                      ) : selectedAccommodation &&
                        searchParams.get("accommodation") ? (
                        // Show only the pre-selected accommodation
                        <div className="mb-4">
                          <AccommodationCard
                            accommodation={selectedAccommodation}
                            isSelected={true}
                            onSelect={setSelectedAccommodation}
                            compact={false}
                          />
                          <div className="mt-4 text-center">
                            <Button
                              variant="outline"
                              onClick={() => {
                                setSelectedAccommodation(null);
                                // Remove accommodation param from URL
                                const newSearchParams = new URLSearchParams(
                                  searchParams
                                );
                                newSearchParams.delete("accommodation");
                                navigate(
                                  `/booking?${newSearchParams.toString()}`,
                                  { replace: true }
                                );
                              }}
                              className="text-[#2C5530] border-[#2C5530] hover:bg-[#2C5530] hover:text-white"
                            >
                              Choose Different Accommodation
                            </Button>
                          </div>
                        </div>
                      ) : (
                        // Show all accommodations for selection
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {accommodations.map((accommodation) => (
                            <AccommodationCard
                              key={accommodation.id}
                              accommodation={accommodation}
                              isSelected={
                                selectedAccommodation?.id === accommodation.id
                              }
                              onSelect={setSelectedAccommodation}
                              compact
                            />
                          ))}
                        </div>
                      )}
                    </div>

                    {/* Date Selection */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                      <div>
                        <label className="text-sm font-medium text-[#2C5530] mb-2 block">
                          Check-in Date
                        </label>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className="w-full justify-start text-left font-normal border-[#2C5530]/30"
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {checkIn
                                ? format(checkIn, "PPP")
                                : "Select check-in date"}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={checkIn}
                              onSelect={setCheckIn}
                              disabled={(date) => date < new Date()}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                      </div>

                      <div>
                        <label className="text-sm font-medium text-[#2C5530] mb-2 block">
                          Check-out Date
                        </label>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className="w-full justify-start text-left font-normal border-[#2C5530]/30"
                            >
                              <CalendarIcon className="mr-2 h-4 w-4" />
                              {checkOut
                                ? format(checkOut, "PPP")
                                : "Select check-out date"}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <Calendar
                              mode="single"
                              selected={checkOut}
                              onSelect={setCheckOut}
                              disabled={(date) => !checkIn || date <= checkIn}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                      </div>
                    </div>

                    {/* Guest Selection */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="text-sm font-medium text-[#2C5530] mb-2 block">
                          Adults
                        </label>
                        <div className="flex items-center gap-3">
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => setAdults(Math.max(1, adults - 1))}
                            disabled={adults <= 1}
                          >
                            <Minus className="h-4 w-4" />
                          </Button>
                          <span className="text-lg font-semibold w-8 text-center">
                            {adults}
                          </span>
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => setAdults(adults + 1)}
                            disabled={
                              selectedAccommodation !== null &&
                              adults + children >=
                                selectedAccommodation.maxGuests
                            }
                          >
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>

                      <div>
                        <label className="text-sm font-medium text-[#2C5530] mb-2 block">
                          Children
                        </label>
                        <div className="flex items-center gap-3">
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() =>
                              setChildren(Math.max(0, children - 1))
                            }
                            disabled={children <= 0}
                          >
                            <Minus className="h-4 w-4" />
                          </Button>
                          <span className="text-lg font-semibold w-8 text-center">
                            {children}
                          </span>
                          <Button
                            variant="outline"
                            size="icon"
                            onClick={() => setChildren(children + 1)}
                            disabled={
                              selectedAccommodation !== null &&
                              adults + children >=
                                selectedAccommodation.maxGuests
                            }
                          >
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>

                    {selectedAccommodation &&
                      guestCount > selectedAccommodation.maxGuests && (
                        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                          This accommodation can accommodate up to{" "}
                          {selectedAccommodation.maxGuests} guests. Please
                          reduce the number of guests or select a different
                          accommodation.
                        </div>
                      )}
                  </CardContent>
                </Card>
              )}

              {/* Step 2: Activities */}
              {currentStep === 2 && (
                <Card className="border-t-4 border-t-[#2C5530]">
                  <CardContent className="p-6">
                    <h3 className="text-2xl font-bold playfair-display-heading text-[#2C5530] mb-6">
                      Select Your Activities
                    </h3>
                    <ActivitySelector
                      activities={activities}
                      selectedActivities={selectedActivities}
                      onActivityToggle={handleActivityToggle}
                      guestCount={guestCount}
                      loading={loadingActivities}
                    />
                  </CardContent>
                </Card>
              )}

              {/* Step 3: Guest Information */}
              {currentStep === 3 && (
                <Card className="border-t-4 border-t-[#2C5530]">
                  <CardContent className="p-6">
                    <h3 className="text-2xl font-bold playfair-display-heading text-[#2C5530] mb-6">
                      Guest Information
                    </h3>
                    <Form {...form}>
                      <form
                        onSubmit={form.handleSubmit(handleCompleteBooking)}
                        className="space-y-6"
                      >
                        {/* Personal Information */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <FormField
                            control={form.control}
                            name="firstName"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-[#2C5530]">
                                  First Name *
                                </FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="Enter your first name"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="lastName"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-[#2C5530]">
                                  Last Name *
                                </FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="Enter your last name"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <FormField
                            control={form.control}
                            name="email"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-[#2C5530]">
                                  Email Address *
                                </FormLabel>
                                <FormControl>
                                  <Input
                                    type="email"
                                    placeholder="<EMAIL>"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="phone"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-[#2C5530]">
                                  Phone Number *
                                </FormLabel>
                                <FormControl>
                                  <Input
                                    placeholder="+****************"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        <FormField
                          control={form.control}
                          name="country"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-[#2C5530]">
                                Country *
                              </FormLabel>
                              <FormControl>
                                <CountrySelect
                                  value={field.value}
                                  onValueChange={field.onChange}
                                  placeholder="Select your country"
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        {/* Emergency Contact */}
                        <div className="border-t pt-6">
                          <h4 className="text-lg font-semibold text-[#2C5530] mb-4">
                            Emergency Contact
                          </h4>
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <FormField
                              control={form.control}
                              name="emergencyContact.name"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="text-[#2C5530]">
                                    Contact Name *
                                  </FormLabel>
                                  <FormControl>
                                    <Input
                                      placeholder="Emergency contact name"
                                      {...field}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name="emergencyContact.phone"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="text-[#2C5530]">
                                    Contact Phone *
                                  </FormLabel>
                                  <FormControl>
                                    <Input
                                      placeholder="Emergency contact phone"
                                      {...field}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                            <FormField
                              control={form.control}
                              name="emergencyContact.relationship"
                              render={({ field }) => (
                                <FormItem>
                                  <FormLabel className="text-[#2C5530]">
                                    Relationship *
                                  </FormLabel>
                                  <FormControl>
                                    <Input
                                      placeholder="e.g., Spouse, Parent, Friend"
                                      {...field}
                                    />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          </div>
                        </div>

                        {/* Special Requests */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <FormField
                            control={form.control}
                            name="specialRequests"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-[#2C5530]">
                                  Special Requests
                                </FormLabel>
                                <FormControl>
                                  <Textarea
                                    placeholder="Any special requests or requirements..."
                                    className="min-h-[100px]"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                          <FormField
                            control={form.control}
                            name="dietaryRequirements"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-[#2C5530]">
                                  Dietary Requirements
                                </FormLabel>
                                <FormControl>
                                  <Textarea
                                    placeholder="Any dietary restrictions or preferences..."
                                    className="min-h-[100px]"
                                    {...field}
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        </div>

                        {/* Terms and Conditions */}
                        <FormField
                          control={form.control}
                          name="termsAccepted"
                          render={({ field }) => (
                            <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                              <FormControl>
                                <Checkbox
                                  checked={field.value}
                                  onCheckedChange={field.onChange}
                                />
                              </FormControl>
                              <div className="space-y-1 leading-none">
                                <FormLabel className="text-[#2C5530]">
                                  I accept the terms and conditions *
                                </FormLabel>
                                <p className="text-sm text-neutral-600">
                                  By checking this box, you agree to our booking
                                  terms, cancellation policy, and privacy
                                  policy. You also confirm that all information
                                  provided is accurate.
                                </p>
                              </div>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </form>
                    </Form>
                  </CardContent>
                </Card>
              )}

              {/* Step 4: Confirmation */}
              {currentStep === 4 && (
                <Card className="border-t-4 border-t-[#2C5530]">
                  <CardContent className="p-6">
                    {isBookingComplete ? (
                      <div className="text-center space-y-6">
                        <div className="flex justify-center">
                          <CheckCircle className="h-16 w-16 text-green-500" />
                        </div>
                        <h3 className="text-3xl font-bold playfair-display-heading text-[#2C5530]">
                          Booking Confirmed!
                        </h3>
                        <p className="text-lg text-neutral-600">
                          Thank you for choosing Malombo Selous Forest Camp.
                          Your safari adventure awaits!
                        </p>

                        <Card className="bg-[#2C5530]/5 border-[#2C5530]">
                          <CardHeader>
                            <CardTitle className="text-[#2C5530] text-center">
                              Booking Reference: {bookingReference}
                            </CardTitle>
                          </CardHeader>
                          <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                              <div className="flex items-center gap-2">
                                <MapPin className="h-4 w-4 text-[#2C5530]" />
                                <span>
                                  <strong>Accommodation:</strong>{" "}
                                  {selectedAccommodation?.title}
                                </span>
                              </div>
                              <div className="flex items-center gap-2">
                                <CalendarIcon className="h-4 w-4 text-[#2C5530]" />
                                <span>
                                  <strong>Dates:</strong>{" "}
                                  {checkIn &&
                                    checkOut &&
                                    `${format(checkIn, "MMM dd")} - ${format(
                                      checkOut,
                                      "MMM dd, yyyy"
                                    )}`}
                                </span>
                              </div>
                              <div className="flex items-center gap-2">
                                <Users className="h-4 w-4 text-[#2C5530]" />
                                <span>
                                  <strong>Guests:</strong> {guestCount} (
                                  {adults} adults, {children} children)
                                </span>
                              </div>
                              <div className="flex items-center gap-2">
                                <span className="text-[#2C5530]">🎯</span>
                                <span>
                                  <strong>Activities:</strong>{" "}
                                  {selectedActivities.length} selected
                                </span>
                              </div>
                            </div>

                            <div className="border-t pt-4 text-center">
                              <div className="text-2xl font-bold text-[#8B4513]">
                                Total: ${pricing.total.toLocaleString()}
                              </div>
                              <div className="text-sm text-neutral-600">
                                All inclusive • {pricing.nights} nights
                              </div>
                            </div>
                          </CardContent>
                        </Card>

                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-left">
                          <h4 className="font-semibold text-blue-800 mb-2">
                            What happens next?
                          </h4>
                          <ul className="text-sm text-blue-700 space-y-1">
                            <li>
                              • You'll receive a confirmation email within 15
                              minutes
                            </li>
                            <li>
                              • Our team will contact you within 24 hours to
                              arrange payment
                            </li>
                            <li>
                              • We'll send you a detailed itinerary 7 days
                              before arrival
                            </li>
                            <li>
                              • 24/7 support is available for any questions
                            </li>
                          </ul>
                        </div>

                        <div className="flex flex-col sm:flex-row gap-4 justify-center">
                          <Button
                            variant="outline"
                            onClick={() => window.print()}
                            className="border-[#2C5530] text-[#2C5530] hover:bg-[#2C5530] hover:text-white"
                          >
                            Print Confirmation
                          </Button>
                          <Button
                            onClick={() => navigate("/")}
                            className="bg-gradient-to-r from-[#2C5530] to-[#8B4513] hover:from-[#8B4513] hover:to-[#2C5530] text-white"
                          >
                            Return to Home
                          </Button>
                        </div>

                        <div className="text-center text-sm text-neutral-500 space-y-2">
                          <p>Need help? Contact us:</p>
                          <div className="flex justify-center gap-4">
                            <div className="flex items-center gap-1">
                              <Mail className="h-4 w-4" />
                              <span><EMAIL></span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Phone className="h-4 w-4" />
                              <span>+255 123 456 789</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center">
                        <h3 className="text-2xl font-bold playfair-display-heading text-[#2C5530] mb-4">
                          Processing your booking...
                        </h3>
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#2C5530] mx-auto"></div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Navigation Buttons */}
              <div className="flex justify-between mt-8">
                <Button
                  variant="outline"
                  onClick={handlePrevStep}
                  disabled={currentStep === 1}
                  className="border-[#2C5530] text-[#2C5530] hover:bg-[#2C5530] hover:text-white"
                >
                  Previous Step
                </Button>
                <Button
                  onClick={handleNextStep}
                  disabled={
                    (currentStep === 1 && !canProceedToStep2) ||
                    (currentStep === 2 && !canProceedToStep3) ||
                    (currentStep === 3 &&
                      (!canProceedToStep4 || isSubmitting)) ||
                    currentStep === 4
                  }
                  className="bg-gradient-to-r from-[#2C5530] to-[#8B4513] hover:from-[#8B4513] hover:to-[#2C5530] text-white"
                >
                  {currentStep === 3 && isSubmitting
                    ? "Processing..."
                    : currentStep === 4
                    ? "Complete Booking"
                    : "Next Step"}
                </Button>
              </div>
            </div>

            {/* Price Calculator Sidebar */}
            <div className="lg:col-span-1">
              {selectedAccommodation && checkIn && checkOut && (
                <PriceCalculator
                  pricing={pricing}
                  accommodationName={selectedAccommodation.title}
                  checkIn={checkIn}
                  checkOut={checkOut}
                  guestCount={guestCount}
                  activitiesCount={selectedActivities.length}
                />
              )}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Booking;
