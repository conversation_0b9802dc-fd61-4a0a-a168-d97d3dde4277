// =====================================================
// Booking Detail View - Complete Management Interface
// =====================================================
// Detailed booking view with comprehensive management capabilities

import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Dialog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Separator } from "@/components/ui/separator";
import {
  ArrowLeft,
  Edit,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Users,
  CreditCard,
  Download,
  Check,
  X,
  Clock,
  DollarSign,
  FileText,
  Activity,
  Settings,
} from "lucide-react";

// Import types and services
import type {
  AdminBooking,
  BookingWithDetails,
  BookingStatus,
  PaymentStatus,
  StatusHistoryEntry,
} from "@/types/booking";
import {
  BOOKING_STATUS_CONFIG,
  PAYMENT_STATUS_CONFIG,
  BOOKING_SOURCE_CONFIG,
  CURRENCY_CONFIG,
} from "@/types/booking";
import {
  getBooking,
  updateBookingStatus,
  updatePaymentStatus,
  exportBookingsToCSV,
  downloadCSV,
} from "@/lib/bookings";
import {
  sendBookingUpdate,
  sendBookingConfirmation,
  sendBookingCancellation,
  sendPaymentReceived,
} from "@/lib/email";

export default function BookingDetail() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  // State management
  const [booking, setBooking] = useState<BookingWithDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);

  // Dialog states
  const [statusDialog, setStatusDialog] = useState({
    open: false,
    newStatus: "" as BookingStatus | "",
  });
  const [paymentDialog, setPaymentDialog] = useState({
    open: false,
    newStatus: "" as PaymentStatus | "",
    method: "",
    reference: "",
  });
  const [emailDialog, setEmailDialog] = useState({
    open: false,
    type: "" as "confirmation" | "update" | "cancellation" | "",
  });

  // Load booking data
  const loadBooking = async () => {
    if (!id) return;

    try {
      setLoading(true);
      const response = await getBooking(id);

      if (response.success) {
        setBooking(response.data);
      } else {
        toast.error(response.message || "Failed to load booking");
        navigate("/admin/bookings");
      }
    } catch (error) {
      console.error("Error loading booking:", error);
      toast.error("Failed to load booking");
      navigate("/admin/bookings");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadBooking();
  }, [id]);

  // Handle status update
  const handleStatusUpdate = async () => {
    if (!booking || !statusDialog.newStatus) return;

    try {
      setUpdating(true);
      const response = await updateBookingStatus(
        booking.id,
        statusDialog.newStatus
      );

      if (response.success) {
        toast.success(response.message);
        await loadBooking();

        // Send appropriate email notification
        if (statusDialog.newStatus === "confirmed") {
          await sendBookingConfirmation(booking);
        } else if (statusDialog.newStatus === "cancelled") {
          await sendBookingCancellation(booking);
        } else {
          await sendBookingUpdate(booking, "status");
        }

        setStatusDialog({ open: false, newStatus: "" });
      } else {
        toast.error(response.message);
      }
    } catch (error) {
      console.error("Error updating status:", error);
      toast.error("Failed to update status");
    } finally {
      setUpdating(false);
    }
  };

  // Handle payment update
  const handlePaymentUpdate = async () => {
    if (!booking || !paymentDialog.newStatus) return;

    try {
      setUpdating(true);
      const response = await updatePaymentStatus(
        booking.id,
        paymentDialog.newStatus,
        paymentDialog.method,
        paymentDialog.reference
      );

      if (response.success) {
        toast.success(response.message);
        await loadBooking();

        // Send payment notification if paid
        if (paymentDialog.newStatus === "paid") {
          await sendPaymentReceived(
            booking,
            booking.total_amount,
            paymentDialog.method
          );
        }

        setPaymentDialog({
          open: false,
          newStatus: "",
          method: "",
          reference: "",
        });
      } else {
        toast.error(response.message);
      }
    } catch (error) {
      console.error("Error updating payment:", error);
      toast.error("Failed to update payment");
    } finally {
      setUpdating(false);
    }
  };

  // Handle email sending
  const handleSendEmail = async () => {
    if (!booking || !emailDialog.type) return;

    try {
      setUpdating(true);
      let result;

      switch (emailDialog.type) {
        case "confirmation":
          result = await sendBookingConfirmation(booking);
          break;
        case "update":
          result = await sendBookingUpdate(booking);
          break;
        case "cancellation":
          result = await sendBookingCancellation(booking);
          break;
        default:
          throw new Error("Invalid email type");
      }

      if (result.success) {
        toast.success(result.message);
      } else {
        toast.error(result.message);
      }

      setEmailDialog({ open: false, type: "" });
    } catch (error) {
      console.error("Error sending email:", error);
      toast.error("Failed to send email");
    } finally {
      setUpdating(false);
    }
  };

  // Handle CSV export
  const handleExport = async () => {
    if (!booking) return;

    try {
      const response = await exportBookingsToCSV({
        columns: [],
        filters: { search: booking.id },
        filename: `booking-${booking.id}-export.csv`,
      });

      if (response.success) {
        downloadCSV(response.data, `booking-${booking.id}-export.csv`);
        toast.success("Booking exported successfully");
      } else {
        toast.error(response.message);
      }
    } catch (error) {
      console.error("Error exporting booking:", error);
      toast.error("Failed to export booking");
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-amber-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading booking details...</p>
        </div>
      </div>
    );
  }

  if (!booking) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-600">Booking not found</p>
        <Button
          variant="outline"
          onClick={() => navigate("/admin/bookings")}
          className="mt-4"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Bookings
        </Button>
      </div>
    );
  }

  const totalGuests =
    booking.adults + booking.children_5_13 + booking.children_0_4;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={() => navigate("/admin/bookings")}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Bookings
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Booking Details
            </h1>
            <p className="text-gray-600 mt-1">Booking ID: {booking.id}</p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" onClick={handleExport}>
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Booking Status</p>
                <Badge
                  className={`mt-1 ${
                    BOOKING_STATUS_CONFIG[booking.status]?.bgColor
                  } ${BOOKING_STATUS_CONFIG[booking.status]?.color}`}
                >
                  {BOOKING_STATUS_CONFIG[booking.status]?.label ||
                    booking.status}
                </Badge>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setStatusDialog({ open: true, newStatus: "" })}
              >
                Update
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Payment Status</p>
                <Badge
                  className={`mt-1 ${
                    PAYMENT_STATUS_CONFIG[booking.payment_status]?.bgColor
                  } ${PAYMENT_STATUS_CONFIG[booking.payment_status]?.color}`}
                >
                  {PAYMENT_STATUS_CONFIG[booking.payment_status]?.label ||
                    booking.payment_status}
                </Badge>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() =>
                  setPaymentDialog({
                    open: true,
                    newStatus: "",
                    method: "",
                    reference: "",
                  })
                }
              >
                Update
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <p className="text-sm text-gray-600">Total Amount</p>
              <p className="text-2xl font-bold text-amber-600 mt-1">
                {CURRENCY_CONFIG[booking.currency]?.symbol}
                {booking.total_amount.toFixed(
                  CURRENCY_CONFIG[booking.currency]?.decimals || 2
                )}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Guest & Party Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="w-5 h-5 mr-2" />
              Guest & Party Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label className="text-sm font-medium text-gray-700">
                Guest Name
              </Label>
              <p className="text-lg font-semibold">{booking.guest_full_name}</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium text-gray-700">
                  Email
                </Label>
                <div className="flex items-center mt-1">
                  <Mail className="w-4 h-4 mr-2 text-gray-500" />
                  <a
                    href={`mailto:${booking.email}`}
                    className="text-blue-600 hover:underline"
                  >
                    {booking.email}
                  </a>
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-700">
                  Phone
                </Label>
                <div className="flex items-center mt-1">
                  <Phone className="w-4 h-4 mr-2 text-gray-500" />
                  <a
                    href={`tel:${booking.phone}`}
                    className="text-blue-600 hover:underline"
                  >
                    {booking.phone}
                  </a>
                </div>
              </div>
            </div>

            <div>
              <Label className="text-sm font-medium text-gray-700">
                Country
              </Label>
              <div className="flex items-center mt-1">
                <MapPin className="w-4 h-4 mr-2 text-gray-500" />
                <p>{booking.country}</p>
              </div>
            </div>

            <Separator />

            <div>
              <Label className="text-sm font-medium text-gray-700">
                Party Composition
              </Label>
              <div className="grid grid-cols-3 gap-4 mt-2">
                <div className="text-center">
                  <p className="text-2xl font-bold text-blue-600">
                    {booking.adults}
                  </p>
                  <p className="text-sm text-gray-500">Adults</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-600">
                    {booking.children_5_13}
                  </p>
                  <p className="text-sm text-gray-500">Children (5-13)</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-purple-600">
                    {booking.children_0_4}
                  </p>
                  <p className="text-sm text-gray-500">Infants (0-4)</p>
                </div>
              </div>
              <p className="text-sm text-gray-600 mt-2 text-center">
                Total: {totalGuests} guest{totalGuests !== 1 ? "s" : ""}
              </p>
            </div>

            {booking.notes_guest && (
              <>
                <Separator />
                <div>
                  <Label className="text-sm font-medium text-gray-700">
                    Guest Notes
                  </Label>
                  <p className="mt-1 text-gray-600 bg-gray-50 p-3 rounded-md">
                    {booking.notes_guest}
                  </p>
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Stay Details */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="w-5 h-5 mr-2" />
              Stay Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label className="text-sm font-medium text-gray-700">
                Accommodation
              </Label>
              <p className="text-lg font-semibold mt-1">
                {booking.accommodation?.name || "Unknown Accommodation"}
              </p>
              {booking.accommodation?.type && (
                <p className="text-sm text-gray-500">
                  Type: {booking.accommodation.type}
                </p>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium text-gray-700">
                  Check-in
                </Label>
                <p className="text-lg font-medium mt-1">
                  {new Date(booking.check_in).toLocaleDateString("en-US", {
                    weekday: "long",
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                  })}
                </p>
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-700">
                  Check-out
                </Label>
                <p className="text-lg font-medium mt-1">
                  {new Date(booking.check_out).toLocaleDateString("en-US", {
                    weekday: "long",
                    year: "numeric",
                    month: "long",
                    day: "numeric",
                  })}
                </p>
              </div>
            </div>

            <div>
              <Label className="text-sm font-medium text-gray-700">
                Duration
              </Label>
              <div className="flex items-center mt-1">
                <Clock className="w-4 h-4 mr-2 text-gray-500" />
                <p className="text-lg font-medium">
                  {booking.nights} night{booking.nights !== 1 ? "s" : ""}
                </p>
              </div>
            </div>

            <Separator />

            <div>
              <Label className="text-sm font-medium text-gray-700">
                Booking Source
              </Label>
              <div className="flex items-center mt-1">
                <span className="text-sm">
                  {BOOKING_SOURCE_CONFIG[booking.source]?.label ||
                    booking.source}
                </span>
              </div>
            </div>

            <div>
              <Label className="text-sm font-medium text-gray-700">
                Created
              </Label>
              <p className="text-sm text-gray-600 mt-1">
                {new Date(booking.created_at).toLocaleString()}
              </p>
            </div>

            {booking.updated_at !== booking.created_at && (
              <div>
                <Label className="text-sm font-medium text-gray-700">
                  Last Updated
                </Label>
                <p className="text-sm text-gray-600 mt-1">
                  {new Date(booking.updated_at).toLocaleString()}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Activities and Pricing */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Activities */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Activity className="w-5 h-5 mr-2" />
              Activities
            </CardTitle>
          </CardHeader>
          <CardContent>
            {booking.activities && booking.activities.length > 0 ? (
              <div className="space-y-3">
                {booking.activities.map((activity, index) => (
                  <div
                    key={activity.id}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                  >
                    <div>
                      <p className="font-medium">{activity.title}</p>
                      <p className="text-sm text-gray-600">
                        {activity.category}
                      </p>
                      {activity.duration && (
                        <p className="text-xs text-gray-500">
                          Duration: {activity.duration}
                        </p>
                      )}
                    </div>
                    {activity.pricing && (
                      <div className="text-right">
                        <p className="text-sm font-medium">
                          {activity.pricing}
                        </p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Activity className="w-12 h-12 mx-auto mb-2 opacity-50" />
                <p>No activities selected</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Pricing Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <DollarSign className="w-5 h-5 mr-2" />
              Pricing Breakdown
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Base Amount</span>
                <span className="font-medium">
                  {CURRENCY_CONFIG[booking.currency]?.symbol}
                  {booking.base_amount.toFixed(
                    CURRENCY_CONFIG[booking.currency]?.decimals || 2
                  )}
                </span>
              </div>

              {booking.discounts > 0 && (
                <div className="flex justify-between text-green-600">
                  <span>Discounts</span>
                  <span>
                    -{CURRENCY_CONFIG[booking.currency]?.symbol}
                    {booking.discounts.toFixed(
                      CURRENCY_CONFIG[booking.currency]?.decimals || 2
                    )}
                  </span>
                </div>
              )}

              {booking.taxes > 0 && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Taxes</span>
                  <span className="font-medium">
                    {CURRENCY_CONFIG[booking.currency]?.symbol}
                    {booking.taxes.toFixed(
                      CURRENCY_CONFIG[booking.currency]?.decimals || 2
                    )}
                  </span>
                </div>
              )}
            </div>

            <Separator />

            <div className="flex justify-between text-lg font-bold">
              <span>Total Amount</span>
              <span className="text-amber-600">
                {CURRENCY_CONFIG[booking.currency]?.symbol}
                {booking.total_amount.toFixed(
                  CURRENCY_CONFIG[booking.currency]?.decimals || 2
                )}
              </span>
            </div>

            <div className="mt-4 p-3 bg-blue-50 rounded-lg">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">Payment Method</span>
                <span className="font-medium">
                  {booking.payment_method || "Not specified"}
                </span>
              </div>
              {booking.payment_reference && (
                <div className="flex items-center justify-between text-sm mt-1">
                  <span className="text-gray-600">Reference</span>
                  <span className="font-medium">
                    {booking.payment_reference}
                  </span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Admin Notes and Status History */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Admin Notes */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="w-5 h-5 mr-2" />
              Admin Notes
            </CardTitle>
          </CardHeader>
          <CardContent>
            {booking.notes_admin ? (
              <div className="bg-yellow-50 p-4 rounded-lg">
                <p className="text-gray-700 whitespace-pre-wrap">
                  {booking.notes_admin}
                </p>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <FileText className="w-12 h-12 mx-auto mb-2 opacity-50" />
                <p>No admin notes</p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Status History */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Clock className="w-5 h-5 mr-2" />
              Status History
            </CardTitle>
          </CardHeader>
          <CardContent>
            {booking.status_history && booking.status_history.length > 0 ? (
              <div className="space-y-3">
                {booking.status_history.map(
                  (entry: StatusHistoryEntry, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                    >
                      <div>
                        <p className="text-sm">
                          <span className="font-medium">
                            {entry.from_status}
                          </span>
                          {" → "}
                          <span className="font-medium">{entry.to_status}</span>
                        </p>
                        <p className="text-xs text-gray-500">
                          {new Date(entry.changed_at).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  )
                )}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Clock className="w-12 h-12 mx-auto mb-2 opacity-50" />
                <p>No status changes recorded</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-3">
            {booking.status === "new" && (
              <Button
                onClick={() =>
                  setStatusDialog({ open: true, newStatus: "confirmed" })
                }
                className="bg-green-600 hover:bg-green-700"
              >
                <Check className="w-4 h-4 mr-2" />
                Confirm Booking
              </Button>
            )}

            {booking.status !== "cancelled" && (
              <Button
                variant="outline"
                onClick={() =>
                  setStatusDialog({ open: true, newStatus: "cancelled" })
                }
                className="text-red-600 hover:text-red-700 border-red-200 hover:border-red-300"
              >
                <X className="w-4 h-4 mr-2" />
                Cancel Booking
              </Button>
            )}

            {booking.payment_status === "unpaid" && (
              <Button
                variant="outline"
                onClick={() =>
                  setPaymentDialog({
                    open: true,
                    newStatus: "paid",
                    method: "",
                    reference: "",
                  })
                }
                className="text-blue-600 hover:text-blue-700"
              >
                <CreditCard className="w-4 h-4 mr-2" />
                Mark as Paid
              </Button>
            )}

            <Button
              variant="outline"
              onClick={() =>
                setEmailDialog({ open: true, type: "confirmation" })
              }
            >
              <Mail className="w-4 h-4 mr-2" />
              Send Confirmation
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Status Update Dialog */}
      <Dialog
        open={statusDialog.open}
        onOpenChange={(open) => setStatusDialog((prev) => ({ ...prev, open }))}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Update Booking Status</DialogTitle>
            <DialogDescription>
              Change the status of this booking. This will send a notification
              email to the guest.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="status-select">New Status</Label>
              <Select
                value={statusDialog.newStatus}
                onValueChange={(value) =>
                  setStatusDialog((prev) => ({
                    ...prev,
                    newStatus: value as BookingStatus,
                  }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select new status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="new">New</SelectItem>
                  <SelectItem value="confirmed">Confirmed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="p-3 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>Current Status:</strong>{" "}
                {BOOKING_STATUS_CONFIG[booking.status]?.label}
              </p>
              {statusDialog.newStatus && (
                <p className="text-sm text-blue-800 mt-1">
                  <strong>New Status:</strong>{" "}
                  {BOOKING_STATUS_CONFIG[statusDialog.newStatus]?.label}
                </p>
              )}
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setStatusDialog({ open: false, newStatus: "" })}
            >
              Cancel
            </Button>
            <Button
              onClick={handleStatusUpdate}
              disabled={!statusDialog.newStatus || updating}
            >
              {updating ? "Updating..." : "Update Status"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Payment Update Dialog */}
      <Dialog
        open={paymentDialog.open}
        onOpenChange={(open) => setPaymentDialog((prev) => ({ ...prev, open }))}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Update Payment Status</DialogTitle>
            <DialogDescription>
              Update the payment status and add payment details.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="payment-status-select">Payment Status</Label>
              <Select
                value={paymentDialog.newStatus}
                onValueChange={(value) =>
                  setPaymentDialog((prev) => ({
                    ...prev,
                    newStatus: value as PaymentStatus,
                  }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select payment status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="unpaid">Unpaid</SelectItem>
                  <SelectItem value="deposit_paid">Deposit Paid</SelectItem>
                  <SelectItem value="paid">Paid</SelectItem>
                  <SelectItem value="refunded">Refunded</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="payment-method">Payment Method</Label>
              <Input
                id="payment-method"
                value={paymentDialog.method}
                onChange={(e) =>
                  setPaymentDialog((prev) => ({
                    ...prev,
                    method: e.target.value,
                  }))
                }
                placeholder="e.g., Credit Card, Bank Transfer, Cash"
              />
            </div>

            <div>
              <Label htmlFor="payment-reference">Payment Reference</Label>
              <Input
                id="payment-reference"
                value={paymentDialog.reference}
                onChange={(e) =>
                  setPaymentDialog((prev) => ({
                    ...prev,
                    reference: e.target.value,
                  }))
                }
                placeholder="Transaction ID, Check number, etc."
              />
            </div>

            <div className="p-3 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>Current Status:</strong>{" "}
                {PAYMENT_STATUS_CONFIG[booking.payment_status]?.label}
              </p>
              {paymentDialog.newStatus && (
                <p className="text-sm text-blue-800 mt-1">
                  <strong>New Status:</strong>{" "}
                  {PAYMENT_STATUS_CONFIG[paymentDialog.newStatus]?.label}
                </p>
              )}
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() =>
                setPaymentDialog({
                  open: false,
                  newStatus: "",
                  method: "",
                  reference: "",
                })
              }
            >
              Cancel
            </Button>
            <Button
              onClick={handlePaymentUpdate}
              disabled={!paymentDialog.newStatus || updating}
            >
              {updating ? "Updating..." : "Update Payment"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Email Dialog */}
      <Dialog
        open={emailDialog.open}
        onOpenChange={(open) => setEmailDialog((prev) => ({ ...prev, open }))}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Send Email Notification</DialogTitle>
            <DialogDescription>
              Send an email notification to the guest about their booking.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="email-type">Email Type</Label>
              <Select
                value={emailDialog.type}
                onValueChange={(value) =>
                  setEmailDialog((prev) => ({ ...prev, type: value as any }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select email type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="confirmation">
                    Booking Confirmation
                  </SelectItem>
                  <SelectItem value="update">Booking Update</SelectItem>
                  <SelectItem value="cancellation">
                    Booking Cancellation
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="p-3 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>Recipient:</strong> {booking.email}
              </p>
              <p className="text-sm text-blue-800 mt-1">
                <strong>Guest:</strong> {booking.guest_full_name}
              </p>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setEmailDialog({ open: false, type: "" })}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSendEmail}
              disabled={!emailDialog.type || updating}
            >
              {updating ? "Sending..." : "Send Email"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
