import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Plus,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Check,
  X,
  CreditCard,
  Mail,
  MoreHorizontal,
  Calendar,
  User,
  Phone,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";

// Import booking types and services
import type {
  BookingWithDetails,
  BookingFilters,
  BookingSort,
  BookingStatus,
  PaymentStatus,
  BookingSource,
} from "@/types/booking";
import {
  BOOKING_STATUS_CONFIG,
  PAYMENT_STATUS_CONFIG,
  BOOKING_SOURCE_CONFIG,
} from "@/types/booking";
import {
  getBookings,
  updateBookingStatus,
  updatePaymentStatus,
  getBookingStatistics,
  exportBookingsToCSV,
  downloadCSV,
} from "@/lib/bookings";
import { sendBookingUpdate, sendPaymentReceived } from "@/lib/email";

export default function BookingsAdmin() {
  const navigate = useNavigate();

  // State management
  const [bookings, setBookings] = useState<BookingWithDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const [statistics, setStatistics] = useState({
    total_bookings: 0,
    confirmed_bookings: 0,
    pending_bookings: 0,
    cancelled_bookings: 0,
    completed_bookings: 0,
    total_revenue: 0,
    paid_revenue: 0,
    pending_revenue: 0,
    average_booking_value: 0,
    total_guests: 0,
  });

  // Filters and sorting
  const [filters, setFilters] = useState<BookingFilters>({
    search: "",
    status: "all",
    payment_status: "all",
    source: "all",
    date_from: "",
    date_to: "",
  });

  const [sort, setSort] = useState<BookingSort>({
    field: "check_in",
    order: "desc",
  });

  // Pagination
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  });

  // UI state
  const [showFilters, setShowFilters] = useState(false);
  const [selectedBooking, setSelectedBooking] =
    useState<BookingWithDetails | null>(null);
  const [actionDialog, setActionDialog] = useState<{
    open: boolean;
    type: "status" | "payment" | "email" | null;
    booking: BookingWithDetails | null;
  }>({
    open: false,
    type: null,
    booking: null,
  });
  const [exporting, setExporting] = useState(false);

  // Load bookings data
  const loadBookings = async () => {
    try {
      setLoading(true);
      const response = await getBookings(filters, sort, pagination);

      if (response.success) {
        setBookings(response.data);
        setPagination(response.pagination);
      } else {
        toast.error(response.message || "Failed to load bookings");
      }
    } catch (error) {
      console.error("Error loading bookings:", error);
      toast.error("Failed to load bookings");
    } finally {
      setLoading(false);
    }
  };

  // Load statistics
  const loadStatistics = async () => {
    try {
      const response = await getBookingStatistics();
      if (response.success) {
        setStatistics(response.data);
      }
    } catch (error) {
      console.error("Error loading statistics:", error);
    }
  };

  // Load data on component mount and when filters/sort/pagination change
  useEffect(() => {
    loadBookings();
  }, [filters, sort, pagination.page]);

  useEffect(() => {
    loadStatistics();
  }, []);

  // Handle search
  const handleSearch = (value: string) => {
    setFilters((prev) => ({ ...prev, search: value }));
    setPagination((prev) => ({ ...prev, page: 1 }));
  };

  // Handle filter changes
  const handleFilterChange = (key: keyof BookingFilters, value: any) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
    setPagination((prev) => ({ ...prev, page: 1 }));
  };

  // Handle sort changes
  const handleSort = (field: BookingSort["field"]) => {
    setSort((prev) => ({
      field,
      order: prev.field === field && prev.order === "asc" ? "desc" : "asc",
    }));
  };

  // Handle quick actions
  const handleQuickAction = async (
    booking: BookingWithDetails,
    action: "confirm" | "cancel" | "mark_paid" | "view" | "email"
  ) => {
    switch (action) {
      case "view":
        navigate(`/admin/bookings/${booking.id}`);
        break;
      case "confirm":
        await handleStatusUpdate(booking, "confirmed");
        break;
      case "cancel":
        await handleStatusUpdate(booking, "cancelled");
        break;
      case "mark_paid":
        await handlePaymentUpdate(booking, "paid");
        break;
      case "email":
        setActionDialog({
          open: true,
          type: "email",
          booking,
        });
        break;
    }
  };

  // Handle status updates
  const handleStatusUpdate = async (
    booking: BookingWithDetails,
    status: BookingStatus
  ) => {
    try {
      const response = await updateBookingStatus(booking.id, status);
      if (response.success) {
        toast.success(response.message);
        await loadBookings();

        // Send email notification
        await sendBookingUpdate(booking, "status");
      } else {
        toast.error(response.message);
      }
    } catch (error) {
      console.error("Error updating booking status:", error);
      toast.error("Failed to update booking status");
    }
  };

  // Handle payment updates
  const handlePaymentUpdate = async (
    booking: BookingWithDetails,
    paymentStatus: PaymentStatus
  ) => {
    try {
      const response = await updatePaymentStatus(booking.id, paymentStatus);
      if (response.success) {
        toast.success(response.message);
        await loadBookings();

        // Send payment notification if paid
        if (paymentStatus === "paid") {
          await sendPaymentReceived(booking, booking.total_amount, "Manual");
        }
      } else {
        toast.error(response.message);
      }
    } catch (error) {
      console.error("Error updating payment status:", error);
      toast.error("Failed to update payment status");
    }
  };

  // Handle CSV export
  const handleExport = async () => {
    try {
      setExporting(true);
      const response = await exportBookingsToCSV({
        columns: [],
        filters,
        filename: `bookings-export-${
          new Date().toISOString().split("T")[0]
        }.csv`,
      });

      if (response.success) {
        downloadCSV(
          response.data,
          `bookings-export-${new Date().toISOString().split("T")[0]}.csv`
        );
        toast.success(response.message);
      } else {
        toast.error(response.message);
      }
    } catch (error) {
      console.error("Error exporting bookings:", error);
      toast.error("Failed to export bookings");
    } finally {
      setExporting(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Bookings</h1>
          <p className="text-gray-600 mt-2">
            Manage guest reservations and bookings
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleExport} disabled={exporting}>
            <Download className="w-4 h-4 mr-2" />
            {exporting ? "Exporting..." : "Export CSV"}
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {statistics.total_bookings}
              </div>
              <div className="text-sm text-gray-500">Total Bookings</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {statistics.confirmed_bookings}
              </div>
              <div className="text-sm text-gray-500">Confirmed</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">
                {statistics.pending_bookings}
              </div>
              <div className="text-sm text-gray-500">Pending</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-600">
                {statistics.completed_bookings}
              </div>
              <div className="text-sm text-gray-500">Completed</div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-amber-600">
                ${statistics.total_revenue.toLocaleString()}
              </div>
              <div className="text-sm text-gray-500">Total Revenue</div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="Search by guest name, email, phone, or booking ID..."
                  value={filters.search}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Filter Toggle */}
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="lg:w-auto"
            >
              <Filter className="w-4 h-4 mr-2" />
              Filters
            </Button>
          </div>

          {/* Expanded Filters */}
          {showFilters && (
            <div className="mt-4 pt-4 border-t grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              <div>
                <Label htmlFor="status-filter">Status</Label>
                <Select
                  value={filters.status}
                  onValueChange={(value) => handleFilterChange("status", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All Statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="new">New</SelectItem>
                    <SelectItem value="confirmed">Confirmed</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="payment-filter">Payment Status</Label>
                <Select
                  value={filters.payment_status}
                  onValueChange={(value) =>
                    handleFilterChange("payment_status", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All Payment Statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Payment Statuses</SelectItem>
                    <SelectItem value="unpaid">Unpaid</SelectItem>
                    <SelectItem value="deposit_paid">Deposit Paid</SelectItem>
                    <SelectItem value="paid">Paid</SelectItem>
                    <SelectItem value="refunded">Refunded</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="source-filter">Source</Label>
                <Select
                  value={filters.source}
                  onValueChange={(value) => handleFilterChange("source", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All Sources" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Sources</SelectItem>
                    <SelectItem value="website">Website</SelectItem>
                    <SelectItem value="email">Email</SelectItem>
                    <SelectItem value="whatsapp">WhatsApp</SelectItem>
                    <SelectItem value="agent">Travel Agent</SelectItem>
                    <SelectItem value="phone">Phone</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="date-from">Check-in From</Label>
                <Input
                  id="date-from"
                  type="date"
                  value={filters.date_from}
                  onChange={(e) =>
                    handleFilterChange("date_from", e.target.value)
                  }
                />
              </div>

              <div>
                <Label htmlFor="date-to">Check-in To</Label>
                <Input
                  id="date-to"
                  type="date"
                  value={filters.date_to}
                  onChange={(e) =>
                    handleFilterChange("date_to", e.target.value)
                  }
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Bookings Table */}
      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort("guest_name")}
                  >
                    Guest
                    {sort.field === "guest_name" && (
                      <span className="ml-1">
                        {sort.order === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort("check_in")}
                  >
                    Dates
                    {sort.field === "check_in" && (
                      <span className="ml-1">
                        {sort.order === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </TableHead>
                  <TableHead>Accommodation</TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort("total_amount")}
                  >
                    Total
                    {sort.field === "total_amount" && (
                      <span className="ml-1">
                        {sort.order === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Payment</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      Loading bookings...
                    </TableCell>
                  </TableRow>
                ) : bookings.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      No bookings found
                    </TableCell>
                  </TableRow>
                ) : (
                  bookings.map((booking) => (
                    <TableRow key={booking.id} className="hover:bg-gray-50">
                      <TableCell>
                        <div>
                          <div className="font-medium">
                            {booking.guest_full_name}
                          </div>
                          <div className="text-sm text-gray-500 flex items-center">
                            <Mail className="w-3 h-3 mr-1" />
                            {booking.email}
                          </div>
                          <div className="text-sm text-gray-500 flex items-center">
                            <Phone className="w-3 h-3 mr-1" />
                            {booking.phone}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">
                            {new Date(booking.check_in).toLocaleDateString()} →
                          </div>
                          <div className="text-sm text-gray-500">
                            {new Date(booking.check_out).toLocaleDateString()}
                          </div>
                          <div className="text-xs text-gray-400">
                            {booking.nights} night
                            {booking.nights !== 1 ? "s" : ""}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">
                            {booking.accommodation?.name || "Unknown"}
                          </div>
                          <div className="text-sm text-gray-500">
                            {booking.adults +
                              booking.children_5_13 +
                              booking.children_0_4}{" "}
                            guest
                            {booking.adults +
                              booking.children_5_13 +
                              booking.children_0_4 !==
                            1
                              ? "s"
                              : ""}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">
                          {booking.currency} {booking.total_amount.toFixed(2)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          className={`${
                            BOOKING_STATUS_CONFIG[booking.status]?.bgColor
                          } ${BOOKING_STATUS_CONFIG[booking.status]?.color}`}
                        >
                          {BOOKING_STATUS_CONFIG[booking.status]?.label ||
                            booking.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge
                          className={`${
                            PAYMENT_STATUS_CONFIG[booking.payment_status]
                              ?.bgColor
                          } ${
                            PAYMENT_STATUS_CONFIG[booking.payment_status]?.color
                          }`}
                        >
                          {PAYMENT_STATUS_CONFIG[booking.payment_status]
                            ?.label || booking.payment_status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {/* Quick Actions */}
                          {booking.status === "new" && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() =>
                                handleQuickAction(booking, "confirm")
                              }
                              className="text-green-600 hover:text-green-700"
                            >
                              <Check className="w-3 h-3" />
                            </Button>
                          )}

                          {booking.payment_status === "unpaid" && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() =>
                                handleQuickAction(booking, "mark_paid")
                              }
                              className="text-blue-600 hover:text-blue-700"
                            >
                              <CreditCard className="w-3 h-3" />
                            </Button>
                          )}

                          {/* More Actions Dropdown */}
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm">
                                <MoreHorizontal className="w-4 h-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem
                                onClick={() =>
                                  handleQuickAction(booking, "view")
                                }
                              >
                                <Eye className="w-4 h-4 mr-2" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem
                                onClick={() =>
                                  navigate(`/admin/bookings/${booking.id}/edit`)
                                }
                              >
                                <Edit className="w-4 h-4 mr-2" />
                                Edit Booking
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem
                                onClick={() =>
                                  handleQuickAction(booking, "email")
                                }
                              >
                                <Mail className="w-4 h-4 mr-2" />
                                Send Email
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              {booking.status !== "confirmed" && (
                                <DropdownMenuItem
                                  onClick={() =>
                                    handleQuickAction(booking, "confirm")
                                  }
                                >
                                  <Check className="w-4 h-4 mr-2" />
                                  Confirm
                                </DropdownMenuItem>
                              )}
                              {booking.status !== "cancelled" && (
                                <DropdownMenuItem
                                  onClick={() =>
                                    handleQuickAction(booking, "cancel")
                                  }
                                  className="text-red-600"
                                >
                                  <X className="w-4 h-4 mr-2" />
                                  Cancel
                                </DropdownMenuItem>
                              )}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Pagination */}
      {pagination.totalPages > 1 && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="text-sm text-gray-500">
                Showing {(pagination.page - 1) * pagination.limit + 1} to{" "}
                {Math.min(pagination.page * pagination.limit, pagination.total)}{" "}
                of {pagination.total} bookings
              </div>

              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setPagination((prev) => ({ ...prev, page: prev.page - 1 }))
                  }
                  disabled={pagination.page <= 1}
                >
                  <ChevronLeft className="w-4 h-4" />
                  Previous
                </Button>

                <div className="flex items-center gap-1">
                  {Array.from(
                    { length: Math.min(5, pagination.totalPages) },
                    (_, i) => {
                      const pageNum =
                        pagination.page <= 3
                          ? i + 1
                          : pagination.page >= pagination.totalPages - 2
                          ? pagination.totalPages - 4 + i
                          : pagination.page - 2 + i;

                      if (pageNum < 1 || pageNum > pagination.totalPages)
                        return null;

                      return (
                        <Button
                          key={pageNum}
                          variant={
                            pageNum === pagination.page ? "default" : "outline"
                          }
                          size="sm"
                          onClick={() =>
                            setPagination((prev) => ({
                              ...prev,
                              page: pageNum,
                            }))
                          }
                          className="w-8 h-8 p-0"
                        >
                          {pageNum}
                        </Button>
                      );
                    }
                  )}
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() =>
                    setPagination((prev) => ({ ...prev, page: prev.page + 1 }))
                  }
                  disabled={pagination.page >= pagination.totalPages}
                >
                  Next
                  <ChevronRight className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Action Dialogs */}
      <Dialog
        open={actionDialog.open}
        onOpenChange={(open) => setActionDialog((prev) => ({ ...prev, open }))}
      >
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {actionDialog.type === "email" && "Send Email"}
              {actionDialog.type === "status" && "Update Status"}
              {actionDialog.type === "payment" && "Update Payment"}
            </DialogTitle>
            <DialogDescription>
              {actionDialog.type === "email" &&
                "Send a notification email to the guest."}
              {actionDialog.type === "status" && "Change the booking status."}
              {actionDialog.type === "payment" && "Update the payment status."}
            </DialogDescription>
          </DialogHeader>

          <div className="py-4">
            {actionDialog.booking && (
              <div className="space-y-2">
                <div>
                  <strong>Guest:</strong> {actionDialog.booking.guest_full_name}
                </div>
                <div>
                  <strong>Booking ID:</strong> {actionDialog.booking.id}
                </div>
                <div>
                  <strong>Dates:</strong>{" "}
                  {new Date(actionDialog.booking.check_in).toLocaleDateString()}{" "}
                  -{" "}
                  {new Date(
                    actionDialog.booking.check_out
                  ).toLocaleDateString()}
                </div>
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() =>
                setActionDialog({ open: false, type: null, booking: null })
              }
            >
              Cancel
            </Button>
            <Button
              onClick={async () => {
                if (actionDialog.booking && actionDialog.type === "email") {
                  // Send email logic would go here
                  toast.success("Email sent successfully");
                  setActionDialog({ open: false, type: null, booking: null });
                }
              }}
            >
              {actionDialog.type === "email" && "Send Email"}
              {actionDialog.type === "status" && "Update Status"}
              {actionDialog.type === "payment" && "Update Payment"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
