-- =====================================================
-- Malombo Booking Management System - Database Schema
-- =====================================================
-- Comprehensive database schema for lodge booking management

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =====================================================
-- Core Tables
-- =====================================================

-- Accommodations table
CREATE TABLE IF NOT EXISTS accommodations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    type VARCHAR(100) NOT NULL,
    description TEXT,
    capacity INTEGER NOT NULL DEFAULT 1,
    amenities JSONB DEFAULT '[]',
    images JSONB DEFAULT '[]',
    pricing JSONB DEFAULT '{}',
    status VARCHAR(50) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID,
    updated_by UUID
);

-- Activities table
CREATE TABLE IF NOT EXISTS activities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100) NOT NULL,
    duration VARCHAR(100),
    difficulty VARCHAR(50),
    max_participants INTEGER,
    min_age INTEGER DEFAULT 0,
    pricing VARCHAR(255),
    images JSONB DEFAULT '[]',
    status VARCHAR(50) DEFAULT 'draft' CHECK (status IN ('draft', 'published', 'archived')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID,
    updated_by UUID
);

-- Bookings table
CREATE TABLE IF NOT EXISTS bookings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    guest_full_name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(50) NOT NULL,
    country VARCHAR(100) NOT NULL,
    adults INTEGER NOT NULL DEFAULT 1 CHECK (adults >= 1),
    children_5_13 INTEGER DEFAULT 0 CHECK (children_5_13 >= 0),
    children_0_4 INTEGER DEFAULT 0 CHECK (children_0_4 >= 0),
    accommodation_id UUID NOT NULL REFERENCES accommodations(id),
    check_in DATE NOT NULL,
    check_out DATE NOT NULL,
    nights INTEGER GENERATED ALWAYS AS (check_out - check_in) STORED,
    activity_ids UUID[] DEFAULT '{}',
    currency VARCHAR(3) DEFAULT 'USD' CHECK (currency IN ('USD', 'EUR', 'GBP', 'TZS')),
    base_amount DECIMAL(10,2) DEFAULT 0,
    discounts DECIMAL(10,2) DEFAULT 0,
    taxes DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(50) DEFAULT 'new' CHECK (status IN ('new', 'confirmed', 'cancelled', 'completed')),
    payment_status VARCHAR(50) DEFAULT 'unpaid' CHECK (payment_status IN ('unpaid', 'deposit_paid', 'paid', 'refunded')),
    payment_method VARCHAR(100),
    payment_reference VARCHAR(255),
    source VARCHAR(50) DEFAULT 'website' CHECK (source IN ('website', 'email', 'whatsapp', 'agent', 'phone')),
    notes_guest TEXT,
    notes_admin TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID,
    updated_by UUID,
    CONSTRAINT valid_dates CHECK (check_out > check_in),
    CONSTRAINT valid_amounts CHECK (total_amount >= 0 AND base_amount >= 0 AND discounts >= 0 AND taxes >= 0)
);

-- Booking status history table
CREATE TABLE IF NOT EXISTS booking_status_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    booking_id UUID NOT NULL REFERENCES bookings(id) ON DELETE CASCADE,
    from_status VARCHAR(50),
    to_status VARCHAR(50) NOT NULL,
    changed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    changed_by UUID,
    notes TEXT
);

-- Settings table
CREATE TABLE IF NOT EXISTS settings (
    key VARCHAR(255) PRIMARY KEY,
    value JSONB NOT NULL,
    description TEXT,
    category VARCHAR(100) NOT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by UUID
);

-- =====================================================
-- Indexes for Performance
-- =====================================================

-- Bookings indexes
CREATE INDEX IF NOT EXISTS idx_bookings_accommodation_id ON bookings(accommodation_id);
CREATE INDEX IF NOT EXISTS idx_bookings_check_in ON bookings(check_in);
CREATE INDEX IF NOT EXISTS idx_bookings_check_out ON bookings(check_out);
CREATE INDEX IF NOT EXISTS idx_bookings_status ON bookings(status);
CREATE INDEX IF NOT EXISTS idx_bookings_payment_status ON bookings(payment_status);
CREATE INDEX IF NOT EXISTS idx_bookings_email ON bookings(email);
CREATE INDEX IF NOT EXISTS idx_bookings_created_at ON bookings(created_at);
CREATE INDEX IF NOT EXISTS idx_bookings_guest_search ON bookings USING gin(to_tsvector('english', guest_full_name || ' ' || email || ' ' || phone));

-- Date range queries for availability
CREATE INDEX IF NOT EXISTS idx_bookings_date_range ON bookings USING gist(daterange(check_in, check_out, '[]'));

-- Activities indexes
CREATE INDEX IF NOT EXISTS idx_activities_category ON activities(category);
CREATE INDEX IF NOT EXISTS idx_activities_status ON activities(status);
CREATE INDEX IF NOT EXISTS idx_activities_title_search ON activities USING gin(to_tsvector('english', title || ' ' || description));

-- Accommodations indexes
CREATE INDEX IF NOT EXISTS idx_accommodations_type ON accommodations(type);
CREATE INDEX IF NOT EXISTS idx_accommodations_status ON accommodations(status);
CREATE INDEX IF NOT EXISTS idx_accommodations_capacity ON accommodations(capacity);
CREATE INDEX IF NOT EXISTS idx_accommodations_name_search ON accommodations USING gin(to_tsvector('english', name || ' ' || description));

-- Settings indexes
CREATE INDEX IF NOT EXISTS idx_settings_category ON settings(category);
CREATE INDEX IF NOT EXISTS idx_settings_is_public ON settings(is_public);

-- Status history indexes
CREATE INDEX IF NOT EXISTS idx_booking_status_history_booking_id ON booking_status_history(booking_id);
CREATE INDEX IF NOT EXISTS idx_booking_status_history_changed_at ON booking_status_history(changed_at);

-- =====================================================
-- Triggers for Updated At
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to all tables with updated_at
DROP TRIGGER IF EXISTS update_accommodations_updated_at ON accommodations;
CREATE TRIGGER update_accommodations_updated_at BEFORE UPDATE ON accommodations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_activities_updated_at ON activities;
CREATE TRIGGER update_activities_updated_at BEFORE UPDATE ON activities FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_bookings_updated_at ON bookings;
CREATE TRIGGER update_bookings_updated_at BEFORE UPDATE ON bookings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_settings_updated_at ON settings;
CREATE TRIGGER update_settings_updated_at BEFORE UPDATE ON settings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- Booking Status History Trigger
-- =====================================================

-- Function to track booking status changes
CREATE OR REPLACE FUNCTION track_booking_status_changes()
RETURNS TRIGGER AS $$
BEGIN
    -- Only track if status actually changed
    IF OLD.status IS DISTINCT FROM NEW.status THEN
        INSERT INTO booking_status_history (booking_id, from_status, to_status, changed_by)
        VALUES (NEW.id, OLD.status, NEW.status, NEW.updated_by);
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply trigger to bookings table
DROP TRIGGER IF EXISTS track_booking_status_changes_trigger ON bookings;
CREATE TRIGGER track_booking_status_changes_trigger
    AFTER UPDATE ON bookings
    FOR EACH ROW
    EXECUTE FUNCTION track_booking_status_changes();

-- =====================================================
-- Default Settings Data
-- =====================================================

-- Insert default settings (only if they don't exist)
-- Lodge contacts
INSERT INTO settings (key, value, description, category, is_public)
SELECT 'lodge_contacts', '{
    "emails": {
        "reservations": "<EMAIL>",
        "sales": "<EMAIL>",
        "info": "<EMAIL>",
        "manager": "<EMAIL>"
    },
    "phones": ["+255 759 511 901", "+255 682 833 104", "+254 781 663 555"],
    "address": {
        "street": "Selous Game Reserve",
        "city": "Morogoro",
        "country": "Tanzania",
        "postal_code": ""
    }
}', 'Lodge contact information', 'contacts', true
WHERE NOT EXISTS (SELECT 1 FROM settings WHERE key = 'lodge_contacts');

-- Check-in policy
INSERT INTO settings (key, value, description, category, is_public)
SELECT 'check_in_policy', '{
    "check_in_time": "12:00",
    "check_in_end": "18:00",
    "check_out_time": "10:00",
    "late_checkout_fee": 50,
    "early_checkin_available": true,
    "early_checkin_fee": 30
}', 'Check-in and check-out policies', 'policies', true
WHERE NOT EXISTS (SELECT 1 FROM settings WHERE key = 'check_in_policy');

-- Child pricing policy
INSERT INTO settings (key, value, description, category, is_public)
SELECT 'child_pricing_policy', '{
    "age_groups": {
        "infant": {
            "min_age": 0,
            "max_age": 4,
            "price_percentage": 0,
            "description": "Children 0-4 years stay free"
        },
        "child": {
            "min_age": 5,
            "max_age": 13,
            "price_percentage": 50,
            "description": "Children 5-13 years pay 50% of adult rate"
        },
        "adult": {
            "min_age": 14,
            "max_age": null,
            "price_percentage": 100,
            "description": "14+ years pay full adult rate"
        }
    },
    "max_children_per_room": 3,
    "infant_bed_required": false
}', 'Child pricing policy configuration', 'policies', true
WHERE NOT EXISTS (SELECT 1 FROM settings WHERE key = 'child_pricing_policy');

-- Pricing defaults
INSERT INTO settings (key, value, description, category, is_public)
SELECT 'pricing_defaults', '{
    "default_currency": "USD",
    "accepted_currencies": ["USD", "EUR", "GBP", "TZS"],
    "tax_rate": 18,
    "service_charge": 0,
    "seasonal_pricing": true,
    "discount_presets": [
        {"name": "Early Bird", "percentage": 10, "description": "Book 60+ days in advance"},
        {"name": "Extended Stay", "percentage": 15, "description": "Stay 7+ nights"},
        {"name": "Repeat Guest", "percentage": 5, "description": "Previous guest discount"},
        {"name": "Group Booking", "percentage": 12, "description": "4+ rooms booked together"}
    ]
}', 'Default pricing configuration', 'pricing', false
WHERE NOT EXISTS (SELECT 1 FROM settings WHERE key = 'pricing_defaults');

-- Email settings
INSERT INTO settings (key, value, description, category, is_public)
SELECT 'email_settings', '{
    "sender_name": "Malombo Selous Forest Camp",
    "sender_email": "<EMAIL>",
    "reply_to": "<EMAIL>",
    "default_cc": [],
    "default_bcc": [],
    "booking_notifications": {
        "new_booking": ["<EMAIL>"],
        "booking_confirmed": ["<EMAIL>"],
        "booking_cancelled": ["<EMAIL>"],
        "payment_received": ["<EMAIL>"]
    }
}', 'Email configuration settings', 'email', false
WHERE NOT EXISTS (SELECT 1 FROM settings WHERE key = 'email_settings');

-- Business info
INSERT INTO settings (key, value, description, category, is_public)
SELECT 'business_info', '{
    "name": "Malombo Selous Forest Camp",
    "tagline": "Experience the Wild Heart of Tanzania",
    "description": "Nestled in the pristine wilderness of Selous Game Reserve, Malombo offers an authentic safari experience with comfortable accommodations and expert guides.",
    "established_year": 2010,
    "license_number": "TZ-SAFARI-2010-001",
    "social_media": {
        "facebook": "https://facebook.com/malombocamp",
        "instagram": "https://instagram.com/malombocamp",
        "twitter": "https://twitter.com/malombocamp"
    },
    "certifications": ["Tanzania Tourism Board", "Selous Conservation Programme"]
}', 'Business information and branding', 'business', true
WHERE NOT EXISTS (SELECT 1 FROM settings WHERE key = 'business_info');

-- =====================================================
-- Comments for Documentation
-- =====================================================

COMMENT ON TABLE accommodations IS 'Lodge accommodations and room types';
COMMENT ON TABLE activities IS 'Available activities and experiences';
COMMENT ON TABLE bookings IS 'Guest reservations and booking details';
COMMENT ON TABLE booking_status_history IS 'Audit trail for booking status changes';
COMMENT ON TABLE settings IS 'System configuration and settings';

COMMENT ON COLUMN bookings.nights IS 'Automatically calculated number of nights';
COMMENT ON COLUMN bookings.activity_ids IS 'Array of selected activity UUIDs';
COMMENT ON COLUMN settings.is_public IS 'Whether setting can be accessed by public API';
