// =====================================================
// Booking Types - Comprehensive TypeScript Definitions
// =====================================================
// Type definitions for both frontend booking and admin management

// Legacy frontend types (kept for compatibility)
export interface Accommodation {
  id: string;
  title: string;
  description: string;
  detailedDescription: string;
  image: string;
  pricePerNight: number;
  maxGuests: number;
  amenities: string[];
  features: string[];
  category: "luxury" | "standard" | "premium";
  size: string;
  bedConfiguration: string;
}

export interface Activity {
  id: string;
  name: string;
  description: string;
  price: number;
  duration: string;
  included: boolean;
  category: "safari" | "cultural" | "adventure" | "relaxation";
  maxParticipants?: number;
  ageRestriction?: string;
}

export interface GuestInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  country: string;
  specialRequests?: string;
  dietaryRequirements?: string;
  emergencyContact: {
    name: string;
    phone: string;
    relationship: string;
  };
}

// =====================================================
// Admin Panel Types
// =====================================================

// Base types for admin panel
export type BookingStatus = "new" | "confirmed" | "cancelled" | "completed";
export type PaymentStatus = "unpaid" | "deposit_paid" | "paid" | "refunded";
export type BookingSource =
  | "website"
  | "email"
  | "whatsapp"
  | "agent"
  | "phone";
export type Currency = "USD" | "EUR" | "GBP" | "TZS";

// Admin guest information (simplified from frontend version)
export interface AdminGuestInfo {
  guest_full_name: string;
  email: string;
  phone: string;
  country: string;
}

// Party composition (aligned with child pricing policy)
export interface PartyComposition {
  adults: number;
  children_5_13: number; // Half price (5-13 years)
  children_0_4: number; // Free (0-4 years)
}

// Stay details
export interface StayDetails {
  accommodation_id: string;
  check_in: string; // ISO date string
  check_out: string; // ISO date string
  nights: number; // Computed field
}

// Pricing breakdown
export interface PricingDetails {
  currency: Currency;
  base_amount: number;
  discounts: number;
  taxes: number;
  total_amount: number;
}

// Payment information
export interface PaymentInfo {
  payment_status: PaymentStatus;
  payment_method?: string;
  payment_reference?: string;
}

// Booking metadata
export interface BookingMeta {
  source: BookingSource;
  notes_admin?: string;
  notes_guest?: string;
}

// Status history entry
export interface StatusHistoryEntry {
  from_status: BookingStatus;
  to_status: BookingStatus;
  changed_at: string; // ISO timestamp
  changed_by?: string; // User ID
}

// Audit information
export interface BookingAudit {
  created_at: string; // ISO timestamp
  updated_at: string; // ISO timestamp
  status_history: StatusHistoryEntry[];
  created_by?: string; // User ID
  updated_by?: string; // User ID
}

// Main admin booking interface
export interface AdminBooking
  extends AdminGuestInfo,
    PartyComposition,
    StayDetails,
    PricingDetails,
    PaymentInfo,
    BookingMeta,
    BookingAudit {
  id: string;
  activity_ids: string[]; // Array of activity UUIDs
  status: BookingStatus;
}

// Booking with populated relations
export interface BookingWithDetails extends AdminBooking {
  accommodation: {
    id: string;
    name: string;
    type: string;
    capacity: number;
    price_range: string;
  };
  activities: Array<{
    id: string;
    title: string;
    category: string;
    pricing: string;
    duration: string;
  }>;
}

// Booking creation request
export interface BookingCreateRequest
  extends AdminGuestInfo,
    PartyComposition,
    Omit<StayDetails, "nights">,
    Omit<PricingDetails, "total_amount">,
    Partial<PaymentInfo>,
    Partial<BookingMeta> {
  activity_ids?: string[];
  status?: BookingStatus;
  total_amount?: number; // Optional, will be calculated if not provided
}

// Booking update request
export interface BookingUpdateRequest extends Partial<BookingCreateRequest> {
  id: string;
}

// Booking filters for list view
export interface BookingFilters {
  search?: string;
  status?: BookingStatus | "all";
  payment_status?: PaymentStatus | "all";
  accommodation_id?: string;
  source?: BookingSource | "all";
  date_from?: string; // ISO date
  date_to?: string; // ISO date
}

// Booking sort options
export interface BookingSort {
  field: "guest_name" | "check_in" | "created_at" | "total_amount";
  order: "asc" | "desc";
}

// Pagination
export interface BookingPagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

// API response types
export interface BookingResponse {
  data: AdminBooking;
  success: boolean;
  message?: string;
}

export interface BookingListResponse {
  data: BookingWithDetails[];
  pagination: BookingPagination;
  success: boolean;
  message?: string;
}

// Booking statistics
export interface BookingStatistics {
  total_bookings: number;
  confirmed_bookings: number;
  pending_bookings: number;
  cancelled_bookings: number;
  completed_bookings: number;
  total_revenue: number;
  paid_revenue: number;
  pending_revenue: number;
  average_booking_value: number;
  total_guests: number;
}

// Legacy frontend types (kept for compatibility)
export interface BookingData {
  accommodation: Accommodation;
  checkIn: Date;
  checkOut: Date;
  guests: {
    adults: number;
    children: number;
  };
  activities: Activity[];
  guestInfo: GuestInfo;
  totalPrice: number;
  nights: number;
  accommodationTotal: number;
  activitiesTotal: number;
  bookingReference?: string;
}

export interface BookingStep {
  id: number;
  title: string;
  description: string;
  isComplete: boolean;
  isActive: boolean;
}

export interface PricingBreakdown {
  accommodationPrice: number;
  nights: number;
  accommodationTotal: number;
  activitiesTotal: number;
  subtotal: number;
  taxes: number;
  total: number;
}

export interface Season {
  id: string;
  name: string;
  startDate: string;
  endDate: string;
  priceMultiplier: number;
}

// =====================================================
// Additional Admin Types
// =====================================================

// CSV export configuration
export interface BookingCSVExportConfig {
  columns: string[];
  dateRange?: {
    from: string;
    to: string;
  };
  filters?: BookingFilters;
  filename?: string;
}

// Email notification types
export type BookingNotificationType =
  | "booking_confirmation"
  | "booking_update"
  | "booking_cancellation"
  | "payment_received";

// Email template variables
export interface BookingEmailVariables {
  guest_name: string;
  booking_id: string;
  check_in_date: string;
  check_out_date: string;
  accommodation_name: string;
  guest_count: string;
  total_amount: string;
  currency: string;
  status: string;
  payment_status: string;
  cancellation_date?: string;
  special_requests?: string;
}

// Booking validation errors
export interface BookingValidationError {
  field: string;
  message: string;
  code: string;
}

// Booking form state
export interface BookingFormState {
  guest: AdminGuestInfo;
  party: PartyComposition;
  stay: Omit<StayDetails, "nights">;
  activities: string[];
  pricing: Omit<PricingDetails, "total_amount">;
  payment: Partial<PaymentInfo>;
  meta: Partial<BookingMeta>;
  errors: BookingValidationError[];
  isSubmitting: boolean;
  isDirty: boolean;
}

// Booking availability check
export interface AvailabilityCheck {
  accommodation_id: string;
  check_in: string;
  check_out: string;
  guests: PartyComposition;
}

export interface AvailabilityResult {
  available: boolean;
  conflicts?: Array<{
    booking_id: string;
    guest_name: string;
    check_in: string;
    check_out: string;
  }>;
  suggestions?: Array<{
    check_in: string;
    check_out: string;
    reason: string;
  }>;
}

// Pricing calculation request
export interface PricingCalculationRequest {
  accommodation_id: string;
  check_in: string;
  check_out: string;
  party: PartyComposition;
  activity_ids: string[];
  currency: Currency;
  discount_code?: string;
}

export interface PricingCalculationResult {
  accommodation_cost: number;
  activities_cost: number;
  subtotal: number;
  discounts: number;
  taxes: number;
  service_charges: number;
  total: number;
  breakdown: Array<{
    item: string;
    quantity: number;
    unit_price: number;
    total_price: number;
  }>;
}

// =====================================================
// Configuration Constants
// =====================================================

// Status and payment badge configurations
export const BOOKING_STATUS_CONFIG: Record<
  BookingStatus,
  {
    label: string;
    color: string;
    bgColor: string;
  }
> = {
  new: {
    label: "New",
    color: "text-blue-700",
    bgColor: "bg-blue-100",
  },
  confirmed: {
    label: "Confirmed",
    color: "text-green-700",
    bgColor: "bg-green-100",
  },
  cancelled: {
    label: "Cancelled",
    color: "text-red-700",
    bgColor: "bg-red-100",
  },
  completed: {
    label: "Completed",
    color: "text-gray-700",
    bgColor: "bg-gray-100",
  },
};

export const PAYMENT_STATUS_CONFIG: Record<
  PaymentStatus,
  {
    label: string;
    color: string;
    bgColor: string;
  }
> = {
  unpaid: {
    label: "Unpaid",
    color: "text-red-700",
    bgColor: "bg-red-100",
  },
  deposit_paid: {
    label: "Deposit Paid",
    color: "text-yellow-700",
    bgColor: "bg-yellow-100",
  },
  paid: {
    label: "Paid",
    color: "text-green-700",
    bgColor: "bg-green-100",
  },
  refunded: {
    label: "Refunded",
    color: "text-purple-700",
    bgColor: "bg-purple-100",
  },
};

// Source configuration
export const BOOKING_SOURCE_CONFIG: Record<
  BookingSource,
  {
    label: string;
    icon: string;
  }
> = {
  website: { label: "Website", icon: "Globe" },
  email: { label: "Email", icon: "Mail" },
  whatsapp: { label: "WhatsApp", icon: "MessageCircle" },
  agent: { label: "Travel Agent", icon: "Users" },
  phone: { label: "Phone", icon: "Phone" },
};

// Currency configuration
export const CURRENCY_CONFIG: Record<
  Currency,
  {
    symbol: string;
    name: string;
    decimals: number;
  }
> = {
  USD: { symbol: "$", name: "US Dollar", decimals: 2 },
  EUR: { symbol: "€", name: "Euro", decimals: 2 },
  GBP: { symbol: "£", name: "British Pound", decimals: 2 },
  TZS: { symbol: "TSh", name: "Tanzanian Shilling", decimals: 0 },
};
