// =====================================================
// Settings Service Layer
// =====================================================
// Service functions for settings management and configuration

import { supabase } from "./supabase";
import type {
  Setting,
  SettingCategory,
  SettingsResponse,
  SettingResponse,
  SettingsUpdateRequest,
  SettingsByCategory,
  BulkSettingsUpdate,
  LodgeContacts,
  CheckInPolicy,
  ChildPricingPolicy,
  CancellationPolicy,
  PricingDefaults,
  EmailSettings,
  EmailTemplates,
  DisplaySettings,
  BusinessInfo,
  EmailTemplate,
} from "@/types/settings";

// =====================================================
// Core Settings Operations
// =====================================================

/**
 * Get all settings
 */
export async function getAllSettings(): Promise<SettingsResponse> {
  try {
    const { data, error } = await supabase.rpc("get_all_settings");

    if (error) throw error;

    const settings: Setting[] =
      data?.map((row: any) => ({
        key: row.key,
        value: row.value,
        description: row.description,
        category: row.category,
        is_public: row.is_public,
        created_at: row.created_at,
        updated_at: row.updated_at,
        updated_by: row.updated_by,
      })) || [];

    return {
      data: settings,
      success: true,
    };
  } catch (error) {
    console.error("Error fetching all settings:", error);
    return {
      data: [],
      success: false,
      message:
        error instanceof Error ? error.message : "Failed to fetch settings",
    };
  }
}

/**
 * Get public settings (for frontend)
 */
export async function getPublicSettings(): Promise<SettingsResponse> {
  try {
    const { data, error } = await supabase.rpc("get_public_settings");

    if (error) throw error;

    const settings: Setting[] =
      data?.map((row: any) => ({
        key: row.key,
        value: row.value,
        description: "",
        category: "general" as SettingCategory,
        is_public: true,
        created_at: "",
        updated_at: "",
        updated_by: "",
      })) || [];

    return {
      data: settings,
      success: true,
    };
  } catch (error) {
    console.error("Error fetching public settings:", error);
    return {
      data: [],
      success: false,
      message:
        error instanceof Error
          ? error.message
          : "Failed to fetch public settings",
    };
  }
}

/**
 * Get settings by category
 */
export async function getSettingsByCategory(
  category: SettingCategory
): Promise<SettingsResponse> {
  try {
    const { data, error } = await supabase.rpc("get_settings_by_category", {
      category_name: category,
    });

    if (error) throw error;

    const settings: Setting[] =
      data?.map((row: any) => ({
        key: row.key,
        value: row.value,
        description: row.description,
        category: category,
        is_public: false,
        created_at: "",
        updated_at: row.updated_at,
        updated_by: "",
      })) || [];

    return {
      data: settings,
      success: true,
    };
  } catch (error) {
    console.error("Error fetching settings by category:", error);
    return {
      data: [],
      success: false,
      message:
        error instanceof Error ? error.message : "Failed to fetch settings",
    };
  }
}

/**
 * Get a specific setting value
 */
export async function getSettingValue(
  key: string
): Promise<{ data: any; success: boolean; message?: string }> {
  try {
    const { data, error } = await supabase.rpc("get_setting_value", {
      setting_key: key,
    });

    if (error) throw error;

    return {
      data: data,
      success: true,
    };
  } catch (error) {
    console.error("Error fetching setting value:", error);
    return {
      data: null,
      success: false,
      message:
        error instanceof Error ? error.message : "Failed to fetch setting",
    };
  }
}

/**
 * Update a single setting
 */
export async function updateSetting(
  key: string,
  value: any
): Promise<{ success: boolean; message: string }> {
  try {
    const { data, error } = await supabase.rpc("update_setting", {
      setting_key: key,
      setting_value: value,
    });

    if (error) throw error;

    return {
      success: true,
      message: "Setting updated successfully",
    };
  } catch (error) {
    console.error("Error updating setting:", error);
    return {
      success: false,
      message:
        error instanceof Error ? error.message : "Failed to update setting",
    };
  }
}

/**
 * Bulk update settings
 */
export async function bulkUpdateSettings(
  settings: Record<string, any>
): Promise<{ success: boolean; message: string; updatedCount?: number }> {
  try {
    const { data, error } = await supabase.rpc("bulk_update_settings", {
      settings_data: settings,
    });

    if (error) throw error;

    return {
      success: true,
      message: `Updated ${data} settings successfully`,
      updatedCount: data,
    };
  } catch (error) {
    console.error("Error bulk updating settings:", error);
    return {
      success: false,
      message:
        error instanceof Error ? error.message : "Failed to update settings",
    };
  }
}

// =====================================================
// Specific Settings Getters
// =====================================================

/**
 * Get lodge contacts
 */
export async function getLodgeContacts(): Promise<{
  data: LodgeContacts;
  success: boolean;
  message?: string;
}> {
  try {
    const response = await getSettingValue("lodge_contacts");
    if (!response.success) throw new Error(response.message);

    return {
      data: response.data as LodgeContacts,
      success: true,
    };
  } catch (error) {
    console.error("Error fetching lodge contacts:", error);
    return {
      data: {
        emails: {
          reservations: "",
          sales: "",
          info: "",
          manager: "",
        },
        phones: [],
        address: {
          street: "",
          city: "",
          country: "",
          postal_code: "",
        },
      },
      success: false,
      message:
        error instanceof Error
          ? error.message
          : "Failed to fetch lodge contacts",
    };
  }
}

/**
 * Get check-in policy
 */
export async function getCheckInPolicy(): Promise<{
  data: CheckInPolicy;
  success: boolean;
  message?: string;
}> {
  try {
    const response = await getSettingValue("check_in_policy");
    if (!response.success) throw new Error(response.message);

    return {
      data: response.data as CheckInPolicy,
      success: true,
    };
  } catch (error) {
    console.error("Error fetching check-in policy:", error);
    return {
      data: {
        check_in_time: "12:00",
        check_in_end: "18:00",
        check_out_time: "10:00",
        late_checkout_fee: 0,
        early_checkin_available: false,
        early_checkin_fee: 0,
      },
      success: false,
      message:
        error instanceof Error
          ? error.message
          : "Failed to fetch check-in policy",
    };
  }
}

/**
 * Get child pricing policy
 */
export async function getChildPricingPolicy(): Promise<{
  data: ChildPricingPolicy;
  success: boolean;
  message?: string;
}> {
  try {
    const response = await getSettingValue("child_pricing_policy");
    if (!response.success) throw new Error(response.message);

    return {
      data: response.data as ChildPricingPolicy,
      success: true,
    };
  } catch (error) {
    console.error("Error fetching child pricing policy:", error);
    return {
      data: {
        age_groups: {
          infant: {
            min_age: 0,
            max_age: 4,
            price_percentage: 0,
            description: "",
          },
          child: {
            min_age: 5,
            max_age: 13,
            price_percentage: 50,
            description: "",
          },
          adult: {
            min_age: 14,
            max_age: null,
            price_percentage: 100,
            description: "",
          },
        },
        max_children_per_room: 3,
        infant_bed_required: false,
      },
      success: false,
      message:
        error instanceof Error
          ? error.message
          : "Failed to fetch child pricing policy",
    };
  }
}

/**
 * Get pricing defaults
 */
export async function getPricingDefaults(): Promise<{
  data: PricingDefaults;
  success: boolean;
  message?: string;
}> {
  try {
    const response = await getSettingValue("pricing_defaults");
    if (!response.success) throw new Error(response.message);

    return {
      data: response.data as PricingDefaults,
      success: true,
    };
  } catch (error) {
    console.error("Error fetching pricing defaults:", error);
    return {
      data: {
        default_currency: "TZS",
        accepted_currencies: ["TZS", "USD", "EUR", "GBP"],
        tax_rate: 18,
        service_charge: 0,
        seasonal_pricing: false,
        discount_presets: [],
      },
      success: false,
      message:
        error instanceof Error
          ? error.message
          : "Failed to fetch pricing defaults",
    };
  }
}

/**
 * Get email settings
 */
export async function getEmailSettings(): Promise<{
  data: EmailSettings;
  success: boolean;
  message?: string;
}> {
  try {
    const response = await getSettingValue("email_settings");
    if (!response.success) throw new Error(response.message);

    return {
      data: response.data as EmailSettings,
      success: true,
    };
  } catch (error) {
    console.error("Error fetching email settings:", error);
    return {
      data: {
        sender_name: "",
        sender_email: "",
        reply_to: "",
        default_cc: [],
        default_bcc: [],
        booking_notifications: {
          new_booking: [],
          booking_confirmed: [],
          booking_cancelled: [],
          payment_received: [],
        },
      },
      success: false,
      message:
        error instanceof Error
          ? error.message
          : "Failed to fetch email settings",
    };
  }
}

/**
 * Get email templates
 */
export async function getEmailTemplates(): Promise<{
  data: EmailTemplates;
  success: boolean;
  message?: string;
}> {
  try {
    const response = await getSettingValue("email_templates");
    if (!response.success) throw new Error(response.message);

    return {
      data: response.data as EmailTemplates,
      success: true,
    };
  } catch (error) {
    console.error("Error fetching email templates:", error);
    return {
      data: {
        booking_confirmation: { subject: "", html_body: "" },
        booking_update: { subject: "", html_body: "" },
        booking_cancellation: { subject: "", html_body: "" },
      },
      success: false,
      message:
        error instanceof Error
          ? error.message
          : "Failed to fetch email templates",
    };
  }
}

/**
 * Get display settings
 */
export async function getDisplaySettings(): Promise<{
  data: DisplaySettings;
  success: boolean;
  message?: string;
}> {
  try {
    const response = await getSettingValue("display_settings");
    if (!response.success) throw new Error(response.message);

    return {
      data: response.data as DisplaySettings,
      success: true,
    };
  } catch (error) {
    console.error("Error fetching display settings:", error);
    return {
      data: {
        theme: {
          primary_color: "#d97706",
          secondary_color: "#92400e",
          accent_color: "#f59e0b",
          background_color: "#fef3c7",
          text_color: "#1f2937",
        },
        logo_url: "",
        favicon_url: "",
        default_images: {
          accommodation_placeholder: "",
          activity_placeholder: "",
        },
        date_format: "YYYY-MM-DD",
        time_format: "HH:mm",
        currency_format: {
          symbol_position: "before",
          decimal_places: 0,
          thousands_separator: ",",
        },
      },
      success: false,
      message:
        error instanceof Error
          ? error.message
          : "Failed to fetch display settings",
    };
  }
}

/**
 * Get business information
 */
export async function getBusinessInfo(): Promise<{
  data: BusinessInfo;
  success: boolean;
  message?: string;
}> {
  try {
    const response = await getSettingValue("business_info");
    if (!response.success) throw new Error(response.message);

    return {
      data: response.data as BusinessInfo,
      success: true,
    };
  } catch (error) {
    console.error("Error fetching business info:", error);
    return {
      data: {
        name: "Malombo Selous Forest Camp",
        tagline: "",
        description: "",
        established_year: 2010,
        license_number: "",
        social_media: {
          facebook: "",
          instagram: "",
          twitter: "",
        },
        certifications: [],
      },
      success: false,
      message:
        error instanceof Error
          ? error.message
          : "Failed to fetch business info",
    };
  }
}

// =====================================================
// Specific Settings Updaters
// =====================================================

/**
 * Update lodge contacts
 */
export async function updateLodgeContacts(
  contacts: LodgeContacts
): Promise<{ success: boolean; message: string }> {
  return updateSetting("lodge_contacts", contacts);
}

/**
 * Update check-in policy
 */
export async function updateCheckInPolicy(
  policy: CheckInPolicy
): Promise<{ success: boolean; message: string }> {
  return updateSetting("check_in_policy", policy);
}

/**
 * Update child pricing policy
 */
export async function updateChildPricingPolicy(
  policy: ChildPricingPolicy
): Promise<{ success: boolean; message: string }> {
  return updateSetting("child_pricing_policy", policy);
}

/**
 * Update pricing defaults
 */
export async function updatePricingDefaults(
  defaults: PricingDefaults
): Promise<{ success: boolean; message: string }> {
  return updateSetting("pricing_defaults", defaults);
}

/**
 * Update email settings
 */
export async function updateEmailSettings(
  settings: EmailSettings
): Promise<{ success: boolean; message: string }> {
  return updateSetting("email_settings", settings);
}

/**
 * Update email templates
 */
export async function updateEmailTemplates(
  templates: EmailTemplates
): Promise<{ success: boolean; message: string }> {
  return updateSetting("email_templates", templates);
}

/**
 * Update display settings
 */
export async function updateDisplaySettings(
  settings: DisplaySettings
): Promise<{ success: boolean; message: string }> {
  return updateSetting("display_settings", settings);
}

/**
 * Update business information
 */
export async function updateBusinessInfo(
  info: BusinessInfo
): Promise<{ success: boolean; message: string }> {
  return updateSetting("business_info", info);
}

// =====================================================
// Email Template Management
// =====================================================

/**
 * Update a specific email template
 */
export async function updateEmailTemplate(
  templateName: string,
  template: EmailTemplate
): Promise<{ success: boolean; message: string }> {
  try {
    const { data, error } = await supabase.rpc("update_email_template", {
      template_name: templateName,
      template_data: template,
    });

    if (error) throw error;

    return {
      success: true,
      message: `Email template '${templateName}' updated successfully`,
    };
  } catch (error) {
    console.error("Error updating email template:", error);
    return {
      success: false,
      message:
        error instanceof Error
          ? error.message
          : "Failed to update email template",
    };
  }
}

/**
 * Get notification contacts for a specific type
 */
export async function getNotificationContacts(
  notificationType: string = "new_booking"
): Promise<{ data: string[]; success: boolean; message?: string }> {
  try {
    const { data, error } = await supabase.rpc("get_notification_contacts", {
      notification_type: notificationType,
    });

    if (error) throw error;

    return {
      data: data || [],
      success: true,
    };
  } catch (error) {
    console.error("Error fetching notification contacts:", error);
    return {
      data: [],
      success: false,
      message:
        error instanceof Error
          ? error.message
          : "Failed to fetch notification contacts",
    };
  }
}

// =====================================================
// Settings Validation and Caching
// =====================================================

/**
 * Validate settings before saving
 */
export function validateSettings(
  category: SettingCategory,
  value: any
): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  switch (category) {
    case "contacts":
      const contacts = value as LodgeContacts;
      if (!contacts.emails?.reservations)
        errors.push("Reservations email is required");
      if (!contacts.emails?.info) errors.push("Info email is required");
      if (!contacts.phones || contacts.phones.length === 0)
        errors.push("At least one phone number is required");
      break;

    case "policies":
      // Add validation for policies
      break;

    case "pricing":
      const pricing = value as PricingDefaults;
      if (!pricing.default_currency)
        errors.push("Default currency is required");
      if (pricing.tax_rate < 0 || pricing.tax_rate > 100)
        errors.push("Tax rate must be between 0 and 100");
      break;

    case "email":
      const email = value as EmailSettings;
      if (!email.sender_email) errors.push("Sender email is required");
      if (!email.sender_name) errors.push("Sender name is required");
      break;

    default:
      break;
  }

  return {
    valid: errors.length === 0,
    errors,
  };
}

/**
 * Settings cache for performance
 */
class SettingsCache {
  private cache: Map<string, { value: any; timestamp: number }> = new Map();
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  get(key: string): any | null {
    const cached = this.cache.get(key);
    if (!cached) return null;

    const now = Date.now();
    if (now - cached.timestamp > this.CACHE_DURATION) {
      this.cache.delete(key);
      return null;
    }

    return cached.value;
  }

  set(key: string, value: any): void {
    this.cache.set(key, {
      value,
      timestamp: Date.now(),
    });
  }

  clear(): void {
    this.cache.clear();
  }

  delete(key: string): void {
    this.cache.delete(key);
  }
}

// Export cache instance
export const settingsCache = new SettingsCache();

/**
 * Get setting with caching
 */
export async function getCachedSettingValue(
  key: string
): Promise<{ data: any; success: boolean; message?: string }> {
  // Check cache first
  const cached = settingsCache.get(key);
  if (cached !== null) {
    return {
      data: cached,
      success: true,
    };
  }

  // Fetch from database
  const result = await getSettingValue(key);
  if (result.success) {
    settingsCache.set(key, result.data);
  }

  return result;
}

/**
 * Update setting and clear cache
 */
export async function updateSettingWithCache(
  key: string,
  value: any
): Promise<{ success: boolean; message: string }> {
  const result = await updateSetting(key, value);
  if (result.success) {
    settingsCache.delete(key);
  }
  return result;
}

/**
 * Reset settings cache
 */
export function clearSettingsCache(): void {
  settingsCache.clear();
}
