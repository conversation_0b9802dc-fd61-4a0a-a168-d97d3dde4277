-- Migration: Increase precision for booking numeric fields
-- This migration increases the precision of numeric fields in the bookings table
-- to accommodate larger TZS currency values

-- Note: This migration is optional and should only be run if you frequently
-- encounter bookings that exceed the current NUMERIC(10,2) limit of 99,999,999.99

-- Backup current data before running this migration
-- CREATE TABLE bookings_backup AS SELECT * FROM bookings;

BEGIN;

-- Increase precision from NUMERIC(10,2) to NUMERIC(12,2)
-- This allows values up to 9,999,999,999.99 (nearly 10 billion)

ALTER TABLE public.bookings 
ALTER COLUMN base_amount TYPE NUMERIC(12,2);

ALTER TABLE public.bookings 
ALTER COLUMN discounts TYPE NUMERIC(12,2);

ALTER TABLE public.bookings 
ALTER COLUMN taxes TYPE NUMERIC(12,2);

ALTER TABLE public.bookings 
ALTER COLUMN total_amount TYPE NUMERIC(12,2);

-- Update constraints to match new precision
ALTER TABLE public.bookings 
DROP CONSTRAINT IF EXISTS bookings_base_amount_check;

ALTER TABLE public.bookings 
ADD CONSTRAINT bookings_base_amount_check 
CHECK (base_amount >= 0 AND base_amount <= 9999999999.99);

ALTER TABLE public.bookings 
DROP CONSTRAINT IF EXISTS bookings_discounts_check;

ALTER TABLE public.bookings 
ADD CONSTRAINT bookings_discounts_check 
CHECK (discounts >= 0 AND discounts <= 9999999999.99);

ALTER TABLE public.bookings 
DROP CONSTRAINT IF EXISTS bookings_taxes_check;

ALTER TABLE public.bookings 
ADD CONSTRAINT bookings_taxes_check 
CHECK (taxes >= 0 AND taxes <= 9999999999.99);

ALTER TABLE public.bookings 
DROP CONSTRAINT IF EXISTS bookings_total_amount_check;

ALTER TABLE public.bookings 
ADD CONSTRAINT bookings_total_amount_check 
CHECK (total_amount >= 0 AND total_amount <= 9999999999.99);

-- Update stored procedures if they exist
-- Note: Update the calculate_booking_totals function to return NUMERIC(12,2)
DROP FUNCTION IF EXISTS calculate_booking_totals(NUMERIC, TEXT);

CREATE OR REPLACE FUNCTION calculate_booking_totals(
    base_amount NUMERIC,
    currency_code TEXT DEFAULT 'USD'
)
RETURNS TABLE (
    base_amount_out NUMERIC(12,2),
    tax_amount NUMERIC(12,2),
    service_charge NUMERIC(12,2),
    total_amount NUMERIC(12,2),
    tax_rate NUMERIC(5,2),
    service_rate NUMERIC(5,2)
) AS $$
DECLARE
    pricing_config JSONB;
    tax_rate_val NUMERIC;
    service_rate_val NUMERIC;
BEGIN
    -- Get pricing configuration
    SELECT value INTO pricing_config
    FROM public.settings
    WHERE key = 'pricing_defaults';

    -- Extract rates (default to 0 if not found)
    tax_rate_val := COALESCE((pricing_config->>'tax_rate')::NUMERIC, 0) / 100.0;
    service_rate_val := COALESCE((pricing_config->>'service_charge')::NUMERIC, 0) / 100.0;

    -- Return calculated values
    RETURN QUERY SELECT
        base_amount::NUMERIC(12,2) as base_amount_out,
        (base_amount * tax_rate_val)::NUMERIC(12,2) as tax_amount,
        (base_amount * service_rate_val)::NUMERIC(12,2) as service_charge,
        (base_amount * (1 + tax_rate_val + service_rate_val))::NUMERIC(12,2) as total_amount,
        (tax_rate_val * 100)::NUMERIC(5,2) as tax_rate,
        (service_rate_val * 100)::NUMERIC(5,2) as service_rate;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION calculate_booking_totals(NUMERIC, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION calculate_booking_totals(NUMERIC, TEXT) TO anon;

COMMIT;

-- Verification queries (run these after migration to verify)
-- SELECT column_name, data_type, numeric_precision, numeric_scale 
-- FROM information_schema.columns 
-- WHERE table_name = 'bookings' 
-- AND column_name IN ('base_amount', 'discounts', 'taxes', 'total_amount');

-- Test the updated function
-- SELECT * FROM calculate_booking_totals(1000000.00, 'TZS');
