@import "tailwindcss";
@import "tw-animate-css";
@import url("https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400..900;1,400..900&display=swap");

@custom-variant dark (&:is(.dark *));

html {
  scroll-behavior: smooth;
}

:root {
  --color-safari-green: #2c5530;
  --color-earth-brown: #8b4513;
  --color-sunset-orange: #ffa07a;
  --color-savanna-gold: #daa520;
  --color-sky-blue: #87ceeb;

  /* Admin panel specific colors */
  --admin-primary: #d97706; /* amber-600 */
  --admin-primary-hover: #b45309; /* amber-700 */
  --admin-sidebar-bg: #ffffff;
  --admin-sidebar-border: #e5e7eb;
  --admin-topbar-bg: #ffffff;
  --admin-content-bg: #f9fafb;
}

.playfair-display-heading {
  font-family: "Playfair Display", serif;
  font-optical-sizing: auto;
  font-weight: 700;
  font-style: normal;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  opacity: 0;
  transform: translateY(20px);
}

.animate-fade-in-up.in-view {
  animation: fadeInUp 0.6s ease-out forwards;
}

.gallery-item {
  position: relative;
  overflow: hidden;
}

.gallery-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 1rem;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-caption {
  transform: translateY(0);
}

/* Mobile Menu Animations */
/* Custom scrollbar for mobile menu */
.sheet-content::-webkit-scrollbar {
  width: 6px;
}

.sheet-content::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.sheet-content::-webkit-scrollbar-thumb {
  background: rgba(161, 98, 7, 0.2);
  border-radius: 3px;
}

.sheet-content::-webkit-scrollbar-thumb:hover {
  background: rgba(161, 98, 7, 0.4);
}

.text-gradient {
  background: linear-gradient(
    45deg,
    var(--color-savanna-gold),
    var(--color-sunset-orange)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent;
}

.highlight-card {
  position: relative;
  overflow: hidden;
}

.highlight-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    45deg,
    var(--color-safari-green),
    var(--color-savanna-gold)
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.highlight-card:hover::before {
  opacity: 0.1;
}

.playfair-display-subheading {
  font-family: "Playfair Display", serif;
  font-optical-sizing: auto;
  font-weight: 600;
  font-style: normal;
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.129 0.042 264.695);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.129 0.042 264.695);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.129 0.042 264.695);
  --primary: oklch(0.208 0.042 265.755);
  --primary-foreground: oklch(0.984 0.003 247.858);
  --secondary: oklch(0.968 0.007 247.896);
  --secondary-foreground: oklch(0.208 0.042 265.755);
  --muted: oklch(0.968 0.007 247.896);
  --muted-foreground: oklch(0.554 0.046 257.417);
  --accent: oklch(0.968 0.007 247.896);
  --accent-foreground: oklch(0.208 0.042 265.755);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.929 0.013 255.508);
  --input: oklch(0.929 0.013 255.508);
  --ring: oklch(0.704 0.04 256.788);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.984 0.003 247.858);
  --sidebar-foreground: oklch(0.129 0.042 264.695);
  --sidebar-primary: oklch(0.208 0.042 265.755);
  --sidebar-primary-foreground: oklch(0.984 0.003 247.858);
  --sidebar-accent: oklch(0.968 0.007 247.896);
  --sidebar-accent-foreground: oklch(0.208 0.042 265.755);
  --sidebar-border: oklch(0.929 0.013 255.508);
  --sidebar-ring: oklch(0.704 0.04 256.788);
}

.dark {
  --background: oklch(0.129 0.042 264.695);
  --foreground: oklch(0.984 0.003 247.858);
  --card: oklch(0.208 0.042 265.755);
  --card-foreground: oklch(0.984 0.003 247.858);
  --popover: oklch(0.208 0.042 265.755);
  --popover-foreground: oklch(0.984 0.003 247.858);
  --primary: oklch(0.929 0.013 255.508);
  --primary-foreground: oklch(0.208 0.042 265.755);
  --secondary: oklch(0.279 0.041 260.031);
  --secondary-foreground: oklch(0.984 0.003 247.858);
  --muted: oklch(0.279 0.041 260.031);
  --muted-foreground: oklch(0.704 0.04 256.788);
  --accent: oklch(0.279 0.041 260.031);
  --accent-foreground: oklch(0.984 0.003 247.858);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.551 0.027 264.364);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.208 0.042 265.755);
  --sidebar-foreground: oklch(0.984 0.003 247.858);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.984 0.003 247.858);
  --sidebar-accent: oklch(0.279 0.041 260.031);
  --sidebar-accent-foreground: oklch(0.984 0.003 247.858);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.551 0.027 264.364);
}

@layer utilities {
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* Admin panel utilities */
  .admin-gradient-bg {
    background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
  }

  .admin-card-hover {
    transition: all 0.2s ease-in-out;
  }

  .admin-card-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04);
  }

  .admin-sidebar-item {
    transition: all 0.15s ease-in-out;
  }

  .admin-sidebar-item:hover {
    background-color: rgba(217, 119, 6, 0.1);
    border-radius: 0.5rem;
  }

  .admin-sidebar-item.active {
    background-color: rgba(217, 119, 6, 0.15);
    color: #d97706;
    border-radius: 0.5rem;
  }

  /* Accommodations specific styles */
  .accommodation-card {
    transition: all 0.2s ease-in-out;
  }

  .accommodation-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .accommodation-image-preview {
    transition: all 0.2s ease-in-out;
  }

  .accommodation-image-preview:hover {
    transform: scale(1.02);
  }

  .accommodation-status-badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
  }

  .accommodation-amenity-chip {
    background-color: rgba(217, 119, 6, 0.1);
    color: #d97706;
    border: 1px solid rgba(217, 119, 6, 0.2);
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
    transition: all 0.15s ease-in-out;
  }

  .accommodation-amenity-chip:hover {
    background-color: rgba(217, 119, 6, 0.15);
    border-color: rgba(217, 119, 6, 0.3);
  }

  /* Mobile responsive table */
  .mobile-table-card {
    @apply block sm:hidden bg-white border border-gray-200 rounded-lg p-4 mb-4 shadow-sm;
  }

  .mobile-table-row {
    @apply flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0;
  }

  .mobile-table-label {
    @apply text-sm font-medium text-gray-600;
  }

  .mobile-table-value {
    @apply text-sm text-gray-900;
  }

  /* Image upload area */
  .image-upload-area {
    border: 2px dashed #d97706;
    background-color: rgba(217, 119, 6, 0.05);
    transition: all 0.2s ease-in-out;
  }

  .image-upload-area:hover {
    border-color: #b45309;
    background-color: rgba(217, 119, 6, 0.1);
  }

  .image-upload-area.dragover {
    border-color: #92400e;
    background-color: rgba(217, 119, 6, 0.15);
  }

  /* Form validation styles */
  .form-error {
    color: #dc2626;
    font-size: 0.875rem;
    margin-top: 0.25rem;
  }

  .form-field-error {
    border-color: #dc2626;
    box-shadow: 0 0 0 1px #dc2626;
  }

  /* Loading states */
  .loading-overlay {
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(2px);
  }

  .loading-spinner {
    border-color: #d97706;
    border-top-color: transparent;
  }
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
